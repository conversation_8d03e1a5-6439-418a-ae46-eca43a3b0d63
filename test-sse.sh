#!/bin/bash

# ChatGLM SSE 测试脚本
# 用于快速验证 SSE 流式响应功能

BASE_URL="http://localhost:8080/v1"

echo "=== ChatGLM SSE 测试脚本 ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否启动
check_service() {
    print_info "检查服务状态..."
    if curl -s -f "$BASE_URL/test/chatglm/health" > /dev/null; then
        print_success "服务正在运行"
        return 0
    else
        print_error "服务未启动或无法访问"
        print_info "请确保应用已启动：./mvnw spring-boot:run"
        return 1
    fi
}

# 测试 ChatGLM 连接
test_connection() {
    print_info "测试 ChatGLM 连接..."
    
    response=$(curl -s "$BASE_URL/test/chatglm/health")
    status=$(echo "$response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    
    if [ "$status" = "healthy" ]; then
        print_success "ChatGLM 连接正常"
        echo "响应: $response"
        return 0
    else
        print_error "ChatGLM 连接失败"
        echo "响应: $response"
        return 1
    fi
}

# 测试 SSE 流式响应
test_sse_stream() {
    print_info "测试 ChatGLM SSE 流式响应..."
    print_info "发送消息: '你好'"
    
    echo
    print_info "=== SSE 响应开始 ==="
    
    # 使用 timeout 命令限制测试时间为 30 秒
    timeout 30s curl -X POST "$BASE_URL/test/chatglm/completions" \
        -H "Content-Type: application/json" \
        -H "Accept: text/event-stream" \
        -d '{
              "model": "glm-4-air",
              "messages": [
                {
                  "role": "user",
                  "content": "你好"
                }
              ],
              "stream": true,
              "temperature": 0.1
            }' \
        --no-buffer \
        -w "\n"
    
    local exit_code=$?
    echo
    print_info "=== SSE 响应结束 ==="
    
    if [ $exit_code -eq 0 ]; then
        print_success "SSE 流式响应测试完成"
    elif [ $exit_code -eq 124 ]; then
        print_warning "SSE 测试超时（30秒），但这可能是正常的"
    else
        print_error "SSE 测试失败，退出码: $exit_code"
    fi
}

# 测试原始 Galaxy AI（用于对比）
test_galaxy_ai() {
    print_info "测试原始 Galaxy AI SSE（用于对比）..."
    print_info "发送消息: '北京天气'"
    
    echo
    print_info "=== Galaxy AI SSE 响应开始 ==="
    
    timeout 30s curl -X POST "$BASE_URL/chat/completions" \
        -H "Content-Type: application/json" \
        -H "Accept: text/event-stream" \
        -d '{
              "model": "demo",
              "messages": [
                {
                  "role": "user",
                  "content": "北京天气"
                }
              ],
              "stream": true,
              "temperature": 0.1
            }' \
        --no-buffer \
        -w "\n"
    
    local exit_code=$?
    echo
    print_info "=== Galaxy AI SSE 响应结束 ==="
    
    if [ $exit_code -eq 0 ]; then
        print_success "Galaxy AI SSE 响应测试完成"
    elif [ $exit_code -eq 124 ]; then
        print_warning "Galaxy AI SSE 测试超时（30秒）"
    else
        print_error "Galaxy AI SSE 测试失败，退出码: $exit_code"
    fi
}

# 主函数
main() {
    echo "开始 SSE 测试流程..."
    echo
    
    # 检查服务状态
    if ! check_service; then
        exit 1
    fi
    
    echo
    
    # 测试连接
    if ! test_connection; then
        print_warning "ChatGLM 连接测试失败，但继续进行 SSE 测试..."
    fi
    
    echo
    
    # 测试 ChatGLM SSE
    test_sse_stream
    
    echo
    print_info "是否要测试原始 Galaxy AI 进行对比？(y/n)"
    read -r answer
    if [ "$answer" = "y" ] || [ "$answer" = "Y" ]; then
        echo
        test_galaxy_ai
    fi
    
    echo
    print_info "测试完成！"
    print_info "请查看上面的输出来分析 SSE 响应是否正常。"
    print_info "如果需要更详细的日志，请查看应用程序的控制台输出。"
}

# 运行主函数
main "$@"
