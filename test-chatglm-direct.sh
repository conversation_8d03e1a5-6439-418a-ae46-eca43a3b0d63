#!/bin/bash

# ChatGLM 直接测试脚本
# 不依赖 Spring Boot，直接测试 ChatGLM API

# 配置
CHATGLM_BASE_URL="https://open.bigmodel.cn/api/paas/v4"
CHATGLM_MODEL="glm-4-air"
CHATGLM_API_KEY="850ee857ad5103d591cfed9284c01c8a.0Bsn7Lku1KzGeKYj"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试 ChatGLM 连接
test_connection() {
    print_info "测试 ChatGLM API 连接..."
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST "$CHATGLM_BASE_URL/chat/completions" \
        -H "Authorization: Bearer $CHATGLM_API_KEY" \
        -H "Content-Type: application/json" \
        -d '{
              "model": "'$CHATGLM_MODEL'",
              "messages": [
                {
                  "role": "user",
                  "content": "Hello"
                }
              ],
              "stream": false,
              "max_tokens": 10
            }')
    
    # 提取 HTTP 状态码
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    response_body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*$//')
    
    if [ "$http_code" = "200" ]; then
        print_success "ChatGLM 连接测试成功 (状态码: $http_code)"
        echo "响应: $response_body"
        return 0
    else
        print_error "ChatGLM 连接测试失败 (状态码: $http_code)"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试 SSE 流式响应
test_sse_stream() {
    local message="$1"
    print_info "测试 ChatGLM SSE 流式响应..."
    print_info "发送消息: '$message'"
    
    echo
    print_info "=== SSE 响应开始 ==="
    
    # 使用 curl 测试 SSE
    curl -X POST "$CHATGLM_BASE_URL/chat/completions" \
        -H "Authorization: Bearer $CHATGLM_API_KEY" \
        -H "Accept: text/event-stream" \
        -H "Content-Type: application/json" \
        -d '{
              "model": "'$CHATGLM_MODEL'",
              "messages": [
                {
                  "role": "user",
                  "content": "'$message'"
                }
              ],
              "stream": true,
              "temperature": 0.1
            }' \
        --no-buffer \
        -w "\n\n状态码: %{http_code}\n总时间: %{time_total}s\n"
    
    local exit_code=$?
    echo
    print_info "=== SSE 响应结束 ==="
    
    if [ $exit_code -eq 0 ]; then
        print_success "SSE 流式响应测试完成"
    else
        print_error "SSE 测试失败，退出码: $exit_code"
    fi
    
    return $exit_code
}

# 对比测试你的私有模型
test_private_model() {
    print_info "测试你的私有模型 SSE（用于对比）..."
    print_info "发送消息: '北京天气'"
    
    echo
    print_info "=== 私有模型 SSE 响应开始 ==="
    
    # 注意：这里使用你原始的测试命令
    curl -X POST --location "http://9.3.64.32:8088/v1/chat/completions" \
        -H "User-Agent: insomnia/10.0.0" \
        -H "Content-Type: application/json" \
        -H "Accept: text/event-stream" \
        -d '{
              "model": "demo",
              "messages": [
                {
                  "role": "user",
                  "content": "北京天气"
                }
              ],
              "stream": true,
              "temperature": 0.1
            }' \
        --no-buffer \
        -w "\n\n状态码: %{http_code}\n总时间: %{time_total}s\n"
    
    local exit_code=$?
    echo
    print_info "=== 私有模型 SSE 响应结束 ==="
    
    if [ $exit_code -eq 0 ]; then
        print_success "私有模型 SSE 响应测试完成"
    else
        print_error "私有模型 SSE 测试失败，退出码: $exit_code"
    fi
    
    return $exit_code
}

# 主函数
main() {
    echo "=== ChatGLM vs 私有模型 SSE 对比测试 ==="
    echo "ChatGLM API: $CHATGLM_BASE_URL"
    echo "ChatGLM Model: $CHATGLM_MODEL"
    echo "ChatGLM API Key: ${CHATGLM_API_KEY:0:10}..."
    echo
    
    # 1. 测试 ChatGLM 连接
    if ! test_connection; then
        print_warning "ChatGLM 连接测试失败，但继续进行 SSE 测试..."
    fi
    
    echo
    
    # 2. 测试 ChatGLM SSE - 简单消息
    print_info "--- 测试 1: ChatGLM 简单消息 ---"
    test_sse_stream "你好"
    
    echo
    
    # 3. 测试 ChatGLM SSE - 北京天气
    print_info "--- 测试 2: ChatGLM 北京天气 ---"
    test_sse_stream "北京天气"
    
    echo
    
    # 4. 询问是否测试私有模型
    print_info "是否要测试你的私有模型进行对比？(y/n)"
    read -r answer
    if [ "$answer" = "y" ] || [ "$answer" = "Y" ]; then
        echo
        print_info "--- 测试 3: 私有模型对比 ---"
        test_private_model
    fi
    
    echo
    print_info "=== 测试总结 ==="
    print_info "1. 如果 ChatGLM 测试成功，说明 SSE 基础功能和网络环境正常"
    print_info "2. 如果私有模型测试失败，可能的原因："
    print_info "   - 超时配置：私有模型响应可能比 ChatGLM 慢"
    print_info "   - 响应格式：检查 SSE 事件格式是否正确"
    print_info "   - 认证问题：Token 获取或刷新机制"
    print_info "   - 网络问题：内网连接、防火墙、代理等"
    print_info "3. 建议对比两个测试的输出格式差异"
    
    echo
    print_success "测试完成！"
}

# 运行主函数
main "$@"
