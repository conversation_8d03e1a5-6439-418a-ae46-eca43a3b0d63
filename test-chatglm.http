### ChatGLM 连接测试
GET http://localhost:8080/v1/test/chatglm/health
Accept: application/json

###

### ChatGLM 流式聊天测试 - 简单问题
POST http://localhost:8080/v1/test/chatglm/completions
Content-Type: application/json
Accept: text/event-stream

{
  "model": "glm-4-air",
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ],
  "stream": true,
  "temperature": 0.1
}

###

### ChatGLM 流式聊天测试 - 北京天气（与原始测试一致）
POST http://localhost:8080/v1/test/chatglm/completions
Content-Type: application/json
Accept: text/event-stream

{
  "model": "glm-4-air",
  "messages": [
    {
      "role": "user",
      "content": "北京天气"
    }
  ],
  "stream": true,
  "temperature": 0.1
}

###

### ChatGLM 流式聊天测试 - 长回答
POST http://localhost:8080/v1/test/chatglm/completions
Content-Type: application/json
Accept: text/event-stream

{
  "model": "glm-4-air",
  "messages": [
    {
      "role": "user",
      "content": "请详细介绍一下 Java 中的 SSE（Server-Sent Events）技术，包括其工作原理、使用场景和实现方式。"
    }
  ],
  "stream": true,
  "temperature": 0.1
}

###

### 原始 Galaxy AI 测试（用于对比）
POST http://localhost:8080/v1/chat/completions
Content-Type: application/json
Accept: text/event-stream

{
  "model": "demo",
  "messages": [
    {
      "role": "user",
      "content": "北京天气"
    }
  ],
  "stream": true,
  "temperature": 0.1
}

###

### 使用 curl 命令测试 ChatGLM（可以在终端运行）
# curl -X POST --location "http://localhost:8080/v1/test/chatglm/completions" \
#     -H "Content-Type: application/json" \
#     -H "Accept: text/event-stream" \
#     -d '{
#           "model": "glm-4-air",
#           "messages": [
#             {
#               "role": "user",
#               "content": "北京天气"
#             }
#           ],
#           "stream": true,
#           "temperature": 0.1
#         }'

###

### 使用 curl 命令测试连接健康状态
# curl -X GET "http://localhost:8080/v1/test/chatglm/health" \
#     -H "Accept: application/json"
