# ChatGLM SSE 测试指南

## 概述

为了验证你的私有化 LLM 模型 API 的 SSE 问题，我创建了一个基于 ChatGLM 的测试实现。这个实现可以帮你：

1. 验证 SSE 流式响应是否正常工作
2. 对比 ChatGLM 和你的私有模型的行为差异
3. 调试 OkHttpClient 超时和连接问题

## 新增的文件

### 1. ChatGlmService.java
- 位置：`src/main/java/cn/com/chinastock/cnf/playbook/service/ChatGlmService.java`
- 功能：封装 ChatGLM API 调用，实现 SSE 流式响应
- 特点：
  - 使用与你的 GalaxyAiService 相同的 OkHttpClient 配置
  - 增加了详细的日志记录用于调试
  - 直接转发 ChatGLM 的 OpenAI 兼容响应

### 2. ChatController 增强
- 新增测试端点：
  - `POST /v1/test/chatglm/completions` - ChatGLM 流式聊天测试
  - `GET /v1/test/chatglm/health` - ChatGLM 连接健康检查

### 3. 测试脚本
- `test-chatglm.http` - HTTP 测试请求集合
- `CHATGLM_TEST_GUIDE.md` - 本测试指南

## 测试步骤

### 第一步：启动应用
```bash
./mvnw spring-boot:run
```

### 第二步：测试连接
```bash
curl -X GET "http://localhost:8080/v1/test/chatglm/health" \
    -H "Accept: application/json"
```

期望响应：
```json
{
  "service": "ChatGLM",
  "status": "healthy",
  "timestamp": **********
}
```

### 第三步：测试 SSE 流式响应
```bash
curl -X POST --location "http://localhost:8080/v1/test/chatglm/completions" \
    -H "Content-Type: application/json" \
    -H "Accept: text/event-stream" \
    -d '{
          "model": "glm-4-air",
          "messages": [
            {
              "role": "user",
              "content": "北京天气"
            }
          ],
          "stream": true,
          "temperature": 0.1
        }'
```

### 第四步：对比测试
同时运行你的原始 Galaxy AI 测试：
```bash
curl -X POST --location "http://localhost:8080/v1/chat/completions" \
    -H "Content-Type: application/json" \
    -H "Accept: text/event-stream" \
    -d '{
          "model": "demo",
          "messages": [
            {
              "role": "user",
              "content": "北京天气"
            }
          ],
          "stream": true,
          "temperature": 0.1
        }'
```

## 调试要点

### 1. 日志分析
ChatGlmService 包含详细的日志记录，关注以下日志：

```
INFO  - Starting ChatGLM stream request with X messages
INFO  - Sending request to ChatGLM API...
INFO  - ChatGLM SSE connection opened, response code: 200
DEBUG - Received SSE event - id: xxx, type: xxx, data: xxx
INFO  - ChatGLM stream completed
```

### 2. 超时配置对比
ChatGlmService 的超时配置：
- connectTimeout: 30秒
- readTimeout: 120秒（比你的 60秒更长）
- writeTimeout: 30秒

### 3. 错误处理
如果 ChatGLM 工作正常但你的私有模型不工作，可能的问题：

1. **API 响应格式不兼容**
   - ChatGLM 返回标准 OpenAI 格式
   - 你的私有模型可能返回不同格式

2. **SSE 事件格式问题**
   - 检查 `data:` 前缀
   - 检查 `[DONE]` 结束标记

3. **超时问题**
   - 私有模型响应可能更慢
   - 需要增加 readTimeout

4. **认证问题**
   - 检查 Token 获取和刷新逻辑
   - 验证请求头设置

## 常见问题排查

### 问题1：ChatGLM 测试失败
- 检查网络连接
- 验证 API Key 是否有效
- 查看错误日志中的具体错误信息

### 问题2：SSE 连接立即断开
- 检查 Accept 头是否为 `text/event-stream`
- 验证服务器是否支持 SSE
- 查看网络代理设置

### 问题3：收不到流式数据
- 检查 `stream: true` 参数
- 验证 EventSourceListener 的实现
- 查看服务器端是否正确发送 SSE 事件

## 下一步调试建议

1. **如果 ChatGLM 测试成功**：
   - 对比两个服务的日志输出
   - 检查私有模型的响应格式
   - 验证私有模型的 SSE 事件格式

2. **如果 ChatGLM 测试也失败**：
   - 检查网络环境
   - 验证 OkHttpClient 配置
   - 测试非流式请求是否正常

3. **性能优化**：
   - 调整超时时间
   - 优化连接池配置
   - 添加重试机制

## 配置说明

ChatGLM 配置（硬编码在 ChatGlmService 中）：
- URL: `https://open.bigmodel.cn/api/paas/v4`
- Model: `glm-4-air`
- API Key: `850ee857ad5103d591cfed9284c01c8a.0Bsn7Lku1KzGeKYj`

如需修改配置，请编辑 `ChatGlmService.java` 中的常量。
