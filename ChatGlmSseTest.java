import com.alibaba.fastjson2.JSON;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * ChatGLM SSE 独立测试类
 * 用于验证 SSE 流式响应功能，不依赖 Spring Boot
 */
public class ChatGlmSseTest {

    // ChatGLM API 配置
    private static final String CHATGLM_BASE_URL = "https://open.bigmodel.cn/api/paas/v4";
    private static final String CHATGLM_MODEL = "glm-4-air";
    private static final String CHATGLM_API_KEY = "850ee857ad5103d591cfed9284c01c8a.0Bsn7Lku1KzGeKYj";

    private final OkHttpClient httpClient;

    public ChatGlmSseTest() {
        // 初始化 HTTP 客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 测试 ChatGLM 连接
     */
    public boolean testConnection() {
        try {
            System.out.println("🔍 测试 ChatGLM API 连接...");
            
            Map<String, Object> testData = new HashMap<>();
            testData.put("model", CHATGLM_MODEL);
            testData.put("messages", List.of(
                Map.of("role", "user", "content", "Hello")
            ));
            testData.put("stream", false);
            testData.put("max_tokens", 10);

            Request request = new Request.Builder()
                    .url(CHATGLM_BASE_URL + "/chat/completions")
                    .header("Authorization", "Bearer " + CHATGLM_API_KEY)
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(
                            JSON.toJSONString(testData),
                            MediaType.get("application/json; charset=utf-8")
                    ))
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                boolean success = response.isSuccessful();
                System.out.println("✅ 连接测试结果: " + (success ? "成功" : "失败") + " (状态码: " + response.code() + ")");
                
                if (!success && response.body() != null) {
                    System.out.println("❌ 错误响应: " + response.body().string());
                }
                return success;
            }
        } catch (Exception e) {
            System.out.println("❌ 连接测试失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试 SSE 流式响应
     */
    public void testSseStream(String message) {
        try {
            System.out.println("\n🚀 开始 SSE 流式测试...");
            System.out.println("📝 发送消息: " + message);

            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("model", CHATGLM_MODEL);
            requestData.put("messages", List.of(
                Map.of("role", "user", "content", message)
            ));
            requestData.put("stream", true);
            requestData.put("temperature", 0.1);

            String requestJson = JSON.toJSONString(requestData);
            System.out.println("📤 请求数据: " + requestJson);

            Request request = new Request.Builder()
                    .url(CHATGLM_BASE_URL + "/chat/completions")
                    .header("Authorization", "Bearer " + CHATGLM_API_KEY)
                    .header("Accept", "text/event-stream")
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(
                            requestJson,
                            MediaType.get("application/json; charset=utf-8")
                    ))
                    .build();

            CountDownLatch latch = new CountDownLatch(1);
            StringBuilder responseBuilder = new StringBuilder();

            EventSources.createFactory(httpClient).newEventSource(request, new EventSourceListener() {
                @Override
                public void onOpen(EventSource eventSource, Response response) {
                    System.out.println("🔗 SSE 连接已打开，响应码: " + response.code());
                    if (!response.isSuccessful()) {
                        System.out.println("❌ API 返回错误: " + response.code() + " " + response.message());
                        try {
                            String errorBody = response.body() != null ? response.body().string() : "无错误详情";
                            System.out.println("❌ 错误详情: " + errorBody);
                        } catch (Exception e) {
                            System.out.println("❌ 无法读取错误详情: " + e.getMessage());
                        }
                        latch.countDown();
                    }
                }

                @Override
                public void onEvent(EventSource eventSource, String id, String type, String data) {
                    System.out.println("📨 收到 SSE 事件:");
                    System.out.println("   ID: " + id);
                    System.out.println("   Type: " + type);
                    System.out.println("   Data: " + data);

                    if ("[DONE]".equals(data)) {
                        System.out.println("✅ SSE 流结束");
                        latch.countDown();
                        return;
                    }

                    if (data != null && !data.trim().isEmpty()) {
                        responseBuilder.append(data).append("\n");
                        
                        // 尝试解析响应内容
                        try {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> response = JSON.parseObject(data, Map.class);
                            @SuppressWarnings("unchecked")
                            List<Map<String, Object>> choices = (List<Map<String, Object>>) response.get("choices");
                            if (choices != null && !choices.isEmpty()) {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> delta = (Map<String, Object>) choices.get(0).get("delta");
                                if (delta != null && delta.containsKey("content")) {
                                    String content = (String) delta.get("content");
                                    if (content != null) {
                                        System.out.print(content);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            System.out.println("⚠️ 解析响应时出错: " + e.getMessage());
                        }
                    }
                }

                @Override
                public void onClosed(EventSource eventSource) {
                    System.out.println("\n🔒 SSE 连接已关闭");
                    latch.countDown();
                }

                @Override
                public void onFailure(EventSource eventSource, Throwable t, Response response) {
                    System.out.println("\n❌ SSE 连接失败: " + t.getMessage());
                    if (response != null) {
                        System.out.println("❌ 响应码: " + response.code() + ", 消息: " + response.message());
                        try {
                            String errorBody = response.body() != null ? response.body().string() : "无错误详情";
                            System.out.println("❌ 错误详情: " + errorBody);
                        } catch (Exception e) {
                            System.out.println("❌ 无法读取错误详情: " + e.getMessage());
                        }
                    }
                    latch.countDown();
                }
            });

            // 等待 SSE 完成，最多等待 60 秒
            boolean completed = latch.await(60, TimeUnit.SECONDS);
            if (!completed) {
                System.out.println("⏰ SSE 测试超时（60秒）");
            }

            System.out.println("\n📋 完整响应数据:");
            System.out.println(responseBuilder.toString());

        } catch (Exception e) {
            System.out.println("❌ SSE 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        ChatGlmSseTest test = new ChatGlmSseTest();

        System.out.println("=== ChatGLM SSE 测试工具 ===");
        System.out.println("API URL: " + CHATGLM_BASE_URL);
        System.out.println("Model: " + CHATGLM_MODEL);
        System.out.println("API Key: " + CHATGLM_API_KEY.substring(0, 10) + "...");
        System.out.println();

        // 1. 测试连接
        if (!test.testConnection()) {
            System.out.println("❌ 连接测试失败，退出测试");
            return;
        }

        // 2. 测试简单消息
        test.testSseStream("你好");

        // 3. 测试北京天气（与原始测试一致）
        test.testSseStream("北京天气");

        System.out.println("\n🎉 测试完成！");
        System.out.println("\n💡 如果 ChatGLM 测试成功，说明 SSE 基础功能正常。");
        System.out.println("💡 如果你的私有模型测试失败，可能的原因：");
        System.out.println("   1. 超时配置不足（私有模型可能响应较慢）");
        System.out.println("   2. 响应格式不兼容（需要检查 SSE 事件格式）");
        System.out.println("   3. 认证机制问题（Token 获取或刷新）");
        System.out.println("   4. 网络连接问题（防火墙、代理等）");
    }
}
