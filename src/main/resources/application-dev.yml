
spring:
  # OkHttp相关配置
  cloud:
    discovery:
      client:
        simple:
          instances:
            loadbalancer-service:
              - uri: http://***********:30688
              - uri: http://***********:30689

  # 数据库配置
  jpa:
    database: ORACLE
    show-sql: false
    open-in-view: false
    hibernate:
      ddl-auto: update  # 生产关闭

  datasource:
    driver-class-name: com.alipay.oceanbase.jdbc.Driver
    url: *************************************
    username: CNF@CNF_Oracle#FFL_ARM_TEST_CLUSTER1
    password: Cnf20241219

    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 3000
      connection-test-query: SELECT 1 FROM DUAL

  # Redis配置
  data:
    redis:
      password: yhplatarch@redistest
      database: 14
      sentinel:
        master: mymaster
        nodes:
          - **********:27001  # Sentinel 节点1
          - **********:27001  # Sentinel 节点2
          - **********:27001  # Sentinel 节点3

# Galaxy Boot相关配置
galaxy:
  feign:
    esb:
      user: user
      password: passsword

  kafka:
    enable: true
    user: cnfcenterx
    password: k6g7f4d9n5d
    server:
      nodes: **********:9092,**********:9092,**********:9092,**********:9092,**********:9092,**********:9092,**********:9092,**********:9092
    log:
      enabled: true
      max-detail-records-count: 10
    jaas:
      enable: true
    consumer:
      batch.listener: false
      is.ack: false
      enable.auto.commit: true


apollo:
  bootstrap:
    enabled: true
  meta: http://10.4.29.16:8080


