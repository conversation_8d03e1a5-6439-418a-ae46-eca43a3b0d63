
spring:
  # 数据库相关配置
  datasource: # 本地启动时切换到当前配置
    url: ********************************************************************************************
    username: cnf
    password: userpassword123!
    driver-class-name: com.mysql.cj.jdbc.Driver

    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 3000
      connection-test-query: SELECT 1 FROM DUAL

  # Redis配置
  data:
    redis:
      database: 0
      sentinel:
        master: mymaster
        nodes:
          - localhost:26379  # Sentinel 节点1
          - localhost:26380  # Sentinel 节点2
          - localhost:26381  # Sentinel 节点3

# Galaxy Boot相关配置
galaxy:
  kafka:
    enable: true
    server:
      nodes: localhost:9092
    log:
      enabled: true
      max-detail-records-count: 10
    jaas:
      enable: false
    consumer:
      batch.listener: false
      is.ack: false
      enable.auto.commit: true

