## Galaxy日志规范

本规范定义了 `GalaxyBoot` 框架的日志标准，包括日志格式、关键字段、日志级别，以及对业务日志和应用运行日志的特殊要求。
通过统一的日志规范，确保满足故障分析、问题定位、性能优化及安全审计的需求。

### 1. 日志需求说明

通过对各个系统的问题分析，目前对`GalaxyBoot`的日志需求主要是以下几个方面：

- 日志规范的统一：目前各个系统打印的日志格式多种多样，从日志收集的角度很难以统一的方式解析，也因此无法将关键信息进行结构化而支持更高效的检索，存在一定的查询性能问题。
- 日志支持全链路追踪：目前无法通过日志查询到一个请求的上下游相关日志，很难定位问题。
- 日志保存时限要求：根据运营标准中日志管理域的要求，对于不同的日志类别有不同的保存时限要求，需要在日志收集和转储过程中进行控制。

本规范将分别从这三个方面对日志相关的设计进行说明。

### 2. 日志格式定义

#### 2.1 应用服务日志内容定义

以下是 `GalaxyBoot` 框架日志的标准格式，适用于所有业务日志和应用运行日志。

| 字段名称                  | 框架记录 | 字段说明      | 	详细描述                                             |
|-----------------------|------|-----------|---------------------------------------------------|
| `version`             | Y    | 日志格式版本    | 初始版本为V1。                                          |
| `timestamp`           | Y    | 时间戳       | 毫秒级，格式为 ISO8601。e.g. 2024-12-08T16:06:17.046+0800 |
| `high_precision_time` | Y    | 高精度时间     | 	纳秒级，用于判断日志打印的先后顺序。e.g. 131070950714083           |
| `log_level`           | N    | 日志级别      | 	日志级别（DEBUG, INFO, WARNING, ERROR），               |
| `thread_id`           | Y    | 线程ID      | 	用于跟踪日志的线程来源。                                     |
| `request_method`      | Y    | 请求方法      | 	API 的HTTP方法名称（POST/GET/PUT/DELETE）。              |
| `request_uri`         | Y    | 请求URI     | 	记录请求API的访问路径。                                    |
| `trace_id`            | Y    | Trace ID  | 	用于分布式系统的链路追踪。                                    |
| `parent_span_id`      | Y    | 父 Span ID | 	用于标识调用链路的上一级。                                    |
| `span_id`             | Y    | Span ID   | 	用于标识当前链路节点。                                      |
| `system_name`         | Y    | 系统名称      | 	当前系统标识（三字码，由数据中心申请和分配）                                                                    |
| `service_name`        | Y    | 服务名称      | 	当前服务名称（默认从 `application.yaml` 的 `spring.application.name` 获取），需要跟APM的service_name保持一致。 |
| `class_name`          | Y    | 类名        | 	记录当前日志的类名。                                       |
| `log_category`        | N    | 日志类别      | 	参见`LogCategory`定义。                               |
| `message`             | N    | 日志消息      | 	详细的日志描述信息，在不同的日志类别下格式不同。                         |
| `stack_trace`         | N    | 异常堆栈      | 	异常堆栈信息（如果存在）。                                    |

#### 2.2 运行容器环境日志内容定义

以下是运行容器环境日志的内容，仅供参考，具体信息请参照MOP实际采集的字段。

| 序号 | 字段名称                | 样例                                                                 |
|----|---------------------|--------------------------------------------------------------------|
| 1  | `container_id`      | `acdea168264a08f9aaca0dfc82ff3551418dfd22d02b713142a6843caa2f61bf` |
| 2  | `container_name`    | `xxx-service-managers`                                             |
| 3  | `host_ip`           | `**********`                                                       |
| 4  | `kube_cluster_name` | `prodA1`                                                           |
| 5  | `namespace`         | `xxx-service`                                                      |
| 6  | `pod_name`          | `xxx-service-managers-v1-64-0-33-6479f897b5-xxx`                   |
| 7  | `node_name`         | `proda1node3.chinastock.com.cn`                                    |

#### 2.3 日志输出约定

1. 应用服务日志会输出到控制台，日志采集需要将控制台日志收集到日志中心。
   `ERROR`/`WARNING`级别日志输出到`stderr`，其他级别日志输出到`stdout`。
2. 每条日志应独立成行，一条完整日志不应使用换行符进行跨行记录；日志中的换行符应转义为`\n`。
3. 单行日志长度建议不超过 5KB，建议仅记录该行日志核心要素，避免记录过多无效内容导致日志质量下降；

#### 2.4 默认日志格式

考虑到日志文本的传输和存储成本，日志格式参照 [公用事件格式 (CEF)](https://www.microfocus.com/documentation/arcsight/arcsight-smartconnectors-8.3/cef-implementation-standard/Content/CEF/Chapter%201%20What%20is%20CEF.htm) 。
Galaxy Boot 默认的日志格式如下：

```text
日志格式版本|时间戳|高精度时间|日志级别|线程ID|请求方法|请求URI|Trace ID|父 Span ID|Span ID|系统名称|服务名称|类名|日志类别|**日志消息**|异常堆栈
```

#### 2.5 日志格式说明
1. 日志的字段之间以`|`进行分隔，其中前14个字段为固定字段，每个字段有固定的含义和格式。
2. 第15个字段为`日志消息`，用于记录日志的具体内容，参考`CEF`中字段`Extension`的格式（`KEY-VAULUE`集合），根据不同的`日志类型`
   提供不同的扩展性，以便记录更多的信息。
3. 第16个字段为`stack_trace`，用于记录异常堆栈信息。

#### 2.6 日志输出示例

```text
V1|2024-12-13T11:09:52.632+0800|303721729927458|INFO|http-nio-8088-exec-3|POST|/api/id|97aa98702ca0400ca4e923232af0dfa0|-|ef281ab249f64b82|EXP|example-service|cn.com.chinastock.cnf.core.log.http.FrameworkLogger|REQUEST_LOG|headers={content-length%3D24, postman-token%3D8a32d392-3d21-4018-b43f-593aceba1ba9, host%3D127.0.0.1:8088, content-type%3Dapplication/json, connection%3Dkeep-alive, accept-encoding%3Dgzip, deflate, br, user-agent%3DPostmanRuntime/7.43.0, accept%3D*/*} body=null query_string=length%3D7 |
V1|2024-12-13T11:09:52.628+0800|303721726574958|ERROR|http-nio-8088-exec-3|POST|/api/id|97aa98702ca0400ca4e923232af0dfa0|-|ef281ab249f64b82|EXP|example-service|cn.com.chinastock.cnf.examples.controller.IdController|BUSINESS_LOG|请求ID长度不支持, length=7, 默认生成8位ID|
V1|2024-12-13T11:09:52.629+0800|303721727260750|INFO|http-nio-8088-exec-3|POST|/api/id|97aa98702ca0400ca4e923232af0dfa0|-|ef281ab249f64b82|EXP|example-service|cn.com.chinastock.cnf.examples.controller.IdController|BUSINESS_LOG|生成8位ID: zXSyhFOj|
V1|2024-12-13T11:09:52.632+0800|303721730048708|INFO|http-nio-8088-exec-3|POST|/api/id|97aa98702ca0400ca4e923232af0dfa0|-|ef281ab249f64b82|EXP|example-service|cn.com.chinastock.cnf.core.log.http.FrameworkLogger|RESPONSE_LOG|http_status_code=200 body={"id":"zXSyhFOj"} |
V1|2024-12-13T11:09:52.632+0800|303721730140416|INFO|http-nio-8088-exec-3|POST|/api/id|97aa98702ca0400ca4e923232af0dfa0|-|ef281ab249f64b82|EXP|example-service|cn.com.chinastock.cnf.core.log.http.FrameworkLogger|PERFORMANCE_LOG|cost=13 unit=ms|
V1|2024-12-13T11:09:55.885+0800|303724983078666|INFO|http-nio-8088-exec-4|POST|/api/id/refresh|e12095b123644298a9712239a586deee|-|350bdc5c141d4725|EXP|example-service|cn.com.chinastock.cnf.core.log.http.FrameworkLogger|REQUEST_LOG|headers={content-length%3D17, postman-token%3D7141779b-3725-4075-99b9-7b9d2c92f863, host%3D127.0.0.1:8088, content-type%3Dapplication/json, connection%3Dkeep-alive, accept-encoding%3Dgzip, deflate, br, user-agent%3DPostmanRuntime/7.43.0, accept%3D*/*} body={    "id":null} query_string=null |
V1|2024-12-13T11:09:55.874+0800|303724972741500|ERROR|http-nio-8088-exec-4|POST|/api/id/refresh|e12095b123644298a9712239a586deee|-|350bdc5c141d4725|EXP|example-service|cn.com.chinastock.cnf.examples.controller.IdController|EXCEPTION_LOG|请求参数异常| java.lang.NullPointerException: Cannot invoke "String.length()" because the return value of "cn.com.chinastock.cnf.examples.dto.IdDTO.getId()" is null\n\t	at cn.com.chinastock.cnf.examples.controller.IdController.createId(IdController.java:35) ~[classes/:?]\n\t	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]\n\t	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]\n\t	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926) ~[spring-webmvc-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831) ~[spring-webmvc-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914) ~[spring-webmvc-6.1.15.jar:6.1.15]\n\t	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590) ~[tomcat-embed-core-10.1.33.jar:6.0]\n\t	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.1.15.jar:6.1.15]\n\t	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.33.jar:6.0]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at cn.com.chinastock.cnf.core.log.http.LogFilter.doFilterInternal(LogFilter.java:51) ~[classes/:?]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.15.jar:6.1.15]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.StandardWrapperValve.invoke(StandardWrapperValve.java:151) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.StandardContextValve.invoke(StandardContextValve.java:74) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:457) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.StandardHostValve.invoke(StandardHostValve.java:99) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.valves.ErrorReportValve.invoke(ErrorReportValve.java:78) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.valves.ErrorReportValve.invoke(ErrorReportValve.java:78) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.StandardEngineValve.invoke(StandardEngineValve.java:57) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.connector.CoyoteAdapter.service(CoyoteAdapter.java:324) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.connector.http11.Http11Processor.service(Http11Processor.java:353) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.connector.AbstractProcessorLight.process(AbstractProcessorLight.java:47) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.connector.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:874) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.web.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1721) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.web.util.net.SocketProcessorBase.run(SocketProcessorBase.java:37) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.web.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.web.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.web.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:48) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]\n\t
V1|2024-12-13T11:09:55.885+0800|303724983200625|INFO|http-nio-8088-exec-4|POST|/api/id/refresh|e12095b123644298a9712239a586deee|-|350bdc5c141d4725|EXP|example-service|cn.com.chinastock.cnf.core.log.http.FrameworkLogger|RESPONSE_LOG|http_status_code=400 body=null |
V1|2024-12-13T11:09:55.885+0800|303724983278250|INFO|http-nio-8088-exec-4|POST|/api/id/refresh|e12095b123644298a9712239a586deee|-|350bdc5c141d4725|EXP|example-service|cn.com.chinastock.cnf.core.log.http.FrameworkLogger|PERFORMANCE_LOG|cost=14 unit=ms|```
```

#### 2.7 LogCategory定义

```java
// 日志类别
public enum LogCategory {
   /**
    * 交易流水日志。记录字段包括用户标识、交易业务参数、交易状态及结果等信息，安全级别（公司数据分类分级）为4级的数据（一般指投资者的账户认证信息，包含投资者用于身份认证的账户编码、密码等）敏感信息应进行脱敏处理。
    */
   TRANSACTION_LOG,
   /**
    * 业务行为日志。记录字段包括业务标识、业务处理耗时、业务请求参数、处理状态及处理结果等信息。
    */
   BUSINESS_LOG,
   /**
    * 应用系统用户操作行为日志。记录系统用户的增加、删除、修改和导出等操作行为，必须记录系统用户对于客户资产和交易数据查询的操作行为，以满足合规、审计、内部控制等工作的需要。
    */
   USER_ACTION_LOG,
   /**
    * 应用运行日志
    */
   APP_LOG,
   /**
    * 框架日志
    */
   FRAMEWORK_LOG,
   /**
    * 异常处理相关日志
    */
   EXCEPTION_LOG,
   /**
    * SQL语句日志
    */
   SQL_LOG,
   /**
    * 请求日志
    */
   REQUEST_LOG,
   /**
    * 响应日志
    */
   RESPONSE_LOG,
   /**
    * 性能相关日志
    */
   PERFORMANCE_LOG
}
```

其中`REQUEST_LOG`/`RESPONSE_LOG`/`PERFORMANCE_LOG`/`SQL_LOG`/`FRAMEWORK_LOG`由框架打印。
其他类型的日志可以由业务代码在不同场景下选择合适的`LogCategory`进行打印。
- `TRANSACTION_LOG`：记录交易流水信息，例如用户标识、交易业务参数、交易状态及结果等信息。
- `BUSINESS_LOG`：记录业务行为信息，例如业务标识、业务处理耗时、业务请求参数、处理状态及处理结果等信息。
- `USER_ACTION_LOG`：记录系统用户的增加、删除、修改和导出等操作行为，必须记录系统用户对于客户资产和交易数据查询的操作行为，以满足合规、审计、内部控制等工作的需要。
- `APP_LOG`：记录其他未归类的系统运行日志。

#### 2.8 可扩展字段说明

对于`REQUEST_LOG`/`RESPONSE_LOG`/`PERFORMANCE_LOG`三种类型的日志，在`message`字段中存储的是`KEY-VALUE`的集合，具体结构定义如下：

2、 `REQUEST_LOG`：存储请求的参数信息，例如`query_string`、`header`和`body`。示例如下：

```text
headers={content-length%3D18, host%3D127.0.0.1:8088, content-type%3Dapplication/json; charset%3Dutf-8, user-agent%3Dcurl/8.7.1, accept%3D*/*} body={"id": "12333333"} query_string=length%3D7
```

2、`RESPONSE_LOG`：存储响应的参数信息，例如`http_status_code`、`meta_code`和`body`。示例如下：

```text
http_status_code=200 meta_code=EXPTCNF401 body={"meta":{"success":false,"code":"EXPTCNF401","message":"用户未认证"},"data":null}
```

3、 `PERFORMANCE_LOG`：存储性能相关的信息，例如`cost`、`unit`。示例如下：

```text
cost=2 unit=ms
```

注：为了方便日志的解析，`REQUEST_LOG`和`RESPONSE_LOG`中的`=`会被转义为其URL编码`%3D`。

#### 2.9 日志输出的脱敏与转义

1. 对于`REQUEST_LOG`、`RESPONSE_LOG`中的敏感字段，需要脱敏处理，如手机号，身份证号，姓名。
   通过注解标识在特定字段上，进行掩码处理（该功能在设计中，暂不支持）。
2. 对于日志中的特殊字符，如`|`，需要进行转义处理，保证能够正常解析。
   默认转义为`%7C`（URL 编码方式），解析时如有必要，可以进行反转义。

### 3. 基于日志的分布式链路追踪

日志内容中记录了`trace_id`、`parent_span_id`、`span_id`，可以通过这三个字段对日志进行链路追踪，从而实现全链路追踪的功能。
为了保证和APM工具的兼容，`trace_id`、`parent_span_id`及`span_id`
的格式定义参考[Trace Context HTTP Headers Format](https://www.w3.org/TR/trace-context/#trace-context-http-headers-format)
的定义。
在具体实现中，不同的APM工具可能有不同的要求，需要留出可扩展的接口，以便应对不同情况。

#### 3.1 格式定义

`trace_id`是整个链路的唯一标识，格式如下：

```
以 16 字节数组表示，例如 4bf92f3577b34da6a3ce929d0e0e4736。
所有字节均为零（000000000000000000000000）将被视为无效值。
```

`parent_span_id`标识了请求的来源，格式如下：

```
以 8 字节数组表示，例如 00f067aa0ba902b7。
所有字节都为零（0000000000000000）将被视为无效值。
```

`span_id`是整个链路在当前系统中的标识，格式同`parent_span_id`。当需要请求其他系统时，会把当前的`span_id`作为
`parent_span_id`传递给下游系统。

#### 3.2 分布式链路的上下文传递

为了实现分布式链路追踪，参照[W3C Trace Context](https://www.w3.org/TR/trace-context/)的规范，要求在HTTP请求头中传递
`trace_id`、`parent_span_id`，格式如下：

```
Header Name: traceparent
Format: traceparent: <version>-<trace-id>-<parent-span-id>-<trace-flags>
Sample: traceparent: 00-0af7651916cd43dd8448eb211c80319c-00f067aa0ba902b7-01
```

`version`和`trace_flags`
的取值参考[Trace Context HTTP Headers Format](https://www.w3.org/TR/trace-context/#trace-context-http-headers-format)
的定义。 无特殊设计情况下，可以分别设置为`00`和`01`。

#### 3.3 其他说明

在GalaxyBoot框架中会默认实现`trace_id`、`parent_span_id`、`span_id`的生成和传递，使用者不需要任何额外编码操作。
规则：

1. 当一个请求进入系统时，如果请求头中没有`traceparent`，则生成一个新的`trace_id`和`span_id`，`parent_span_id`为空。
2. 当一个请求进入系统时，如果请求头中有`traceparent`，则解析`traceparent`中的`trace_id`和`parent_span_id`，生成一个新的
   `span_id`。
3. 当调用其他系统时，将当前的`span_id`作为`parent_span_id`，同`trace_id`通过`traceparent`传递给其他系统。

### 4. 日志保存时限

#### 4.1 日志保存时限要求

在文档[《附件1：新版日志管理域标准.xlsx》](https://cloud.chinastock.com.cn/d/home#/sandbox/5a41/1f92b3c875391027/%2FIT%E6%8A%80%E6%9C%AF%E6%9E%B6%E6%9E%84%E6%B2%BB%E7%90%86%E4%BA%8C%E6%9C%9F%2F%E8%BF%90%E8%90%A5%E6%A0%87%E5%87%86/?previewingFileName=%E9%99%84%E4%BB%B61%EF%BC%9A%E6%96%B0%E7%89%88%E6%97%A5%E5%BF%97%E7%AE%A1%E7%90%86%E5%9F%9F%E6%A0%87%E5%87%86.xlsx)
中提出了11类日志（交易流水日志，业务行为日志，应用系统用户操作行为日志，应用运行日志，数据库操作行为记录，数据库运行日志，中间件日志，操作系统日志，网络设备日志，信息安全设备日志）的记录要求，
这里讨论的日志主要是 **交易流水日志，业务行为日志，应用系统用户操作行为日志，应用运行日志** 这4类日志的输出内容要求，其中*
*交易流水日志和应用系统用户操作行为日志**只在一些特定的系统中存在，其他日志应通过相应的中间件或系统进行记录和获取，但从日志收集的信息上可以保持统一。

其中：

- 交易流水日志、业务行为日志、应用系统用户操作行为日志，可归类为业务日志，具体的记录内容要求需要业务服务根据要求在日志信息中详细记录。
- 应用运行日志 主要记录应用运行过程中的一些关键信息，尽量通过开发框架进行记录，以保证日志的统一性。

具体的日志保存时限参照[《附件1：新版日志管理域标准.xlsx》](https://cloud.chinastock.com.cn/d/home#/sandbox/5a41/1f92b3c875391027/%2FIT%E6%8A%80%E6%9C%AF%E6%9E%B6%E6%9E%84%E6%B2%BB%E7%90%86%E4%BA%8C%E6%9C%9F%2F%E8%BF%90%E8%90%A5%E6%A0%87%E5%87%86/?previewingFileName=%E9%99%84%E4%BB%B61%EF%BC%9A%E6%96%B0%E7%89%88%E6%97%A5%E5%BF%97%E7%AE%A1%E7%90%86%E5%9F%9F%E6%A0%87%E5%87%86.xlsx)

#### 4.2 日志采集和转储

在《附件1：新版日志管理域标准.xlsx》中规定了不同日志类型，以及不同级别的系统对日志的保存和转储要求。
为了实现这些要求，需要在日志收集和转储过程中对日志进行分类，根据日志的`system_name`、`service_name`和`log_category`
字段进行分类，然后根据不同的日志类别进行保存和转储。
其中`system_name`为系统的三字码，`service_name`为该系统下具体的服务名称，这两个字段用于标识日志的来源，在具体实现层面可以通过
**配置文件或环境变量**进行设置（*待定）。
`log_category`表示日志的不同类型，在框架中会提供枚举类，便于在日志打印时进行设置。 枚举的具体定义如下：
