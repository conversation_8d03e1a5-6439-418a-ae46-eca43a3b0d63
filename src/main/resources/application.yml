server:
  port: 8088

# Spring相关配置
spring:
  application:
    name: galaxy-boot-playbook-service

  profiles:
    active: dev

  # OkHttp相关配置
  cloud:
    openfeign:
      httpclient:
        maxConnections: 100
        timeToLive: 600
        timeToLiveUnit: SECONDS
      client:
        config:
          default:
            connectTimeout: 2000   # 单位：毫秒
            readTimeout: 10000     # 单位：毫秒
            writeTimeout: 10000     # 单位：毫秒
            loggerLevel: full
    loadbalancer:      # 需要loadbalancer时配置
      enabled: true
      retry:
        enabled: false

  # LocalCache的配置
  cache:
    type: caffeine
    caffeine:
      # 这是一个全局的、默认的缓存配置
      # 所有未被特殊指定的配置项都将从此继承
      spec: > # 使用 spec 字符串进行详细配置，非常灵活
        maximumSize=500,
        expireAfterWrite=5s,
        recordStats
  # 自定义的、按缓存名区分的特殊配置
  custom-caffeine:
    specs:
      # 'longTermCache' 的特殊配置
      # 它会覆盖默认的 expireAfterWrite，并添加 initialCapacity
      # 但它会继承默认的 maximumSize 和 recordStats
      longTermCache:
        spec: initialCapacity=200,expireAfterWrite=10m

      # 'shortTermCache' 的特殊配置
      # 它只覆盖 maximumSize，其他配置全部继承自默认值
      shortTermCache:
        spec: maximumSize=500

# Galaxy Boot相关配置
galaxy:
  system:
    code: CNF  # 系统三字码，需配置到该配置文件下，不支持apollo配置

  swagger:
    auth:
      username: admin
      password: admin@2015

  feign:
    esb:
      user: user
      password: passsword

  log:
    request-response:
      enabled: true            # 是否打印RequestLog和ResponseLog，默认为true
      request-headers: true    # 是否打印Request Header，默认不打印(仅在request-response.enabled=true时生效)
      response-headers: false  # 是否打印Response Header，默认不打印(仅在request-response.enabled=true时生效)
      mask-field: false         # 是否开启RequestLog和ResponseLog中字段的掩码处理，默认为false。设置为true后，被注解@MaskedField标记的字段会被掩码处理为"***"
    performance:
      enabled: true            # 是否打印性能日志，默认为true
    default-category: APP_LOG
    exception-pretty-print: true # 开启后，会将异常日志多打一条换行后的日志，生产关闭

  metrics:
    datasource:
      prometheus:
        enable: true

  datasource:
    dynamic-maximum-pool-size: true         # 开启后，可通过 Apollo 配置中心动态更新连接池最大连接数配置

  security:
    input-protect: true   # 对请求内容进行转义，主要处理 xss 攻击
    output-protect: true  # 对响应内容进行转义
    actuator: # Actuator 端点保护配置
      protect: true       # 是否开启 Actuator 保护，默认为 false
#      whitelist: # Actuator 白名单 IP
#        - 127.0.0.1
#        - 0:0:0:0:0:0:0:1  # 表示 `localhost` 的 IP 地址。
#        - **********/16    # 表示从 `***********` 到 `*************` 的所有 IP 地址。
#        - ********/16
  ai:
    enabled: true
    base-url: http://copilot.tsolph.chinastock.com.cn
    system-id: sys_cnf
    system-secret: VsJjm+PmoFn+VjF6-6bI1R9yU9ornxJ5
    account: shixiaolong_it
    app-id: app-5D55owR6HjNVPfCvsaYQKJ
    connect-timeout: 60000
    read-timeout: 60000
    write-timeout: 60000

# Apollo相关配置
app:
  id: galaxy-boot-playbook

# actuator & metrics相关配置
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: prometheus

  metrics:
    tags:
      application: ${spring.application.name}
      region: my-region

  prometheus:
    metrics:
      export:
        enabled: true

# 日志级别相关配置
logging:
  level:
    cn.com.chinastock.cnf.playbook.client.ESBClient: DEBUG
    cn.com.chinastock.cnf.playbook.client.LoadBalancerClient: DEBUG
    cn.com.chinastock.cnf.playbook.client.BdpClient: DEBUG
    com.ctrip.framework.apollo.spring.annotation.SpringValueProcessor: WARN
    org.springframework.kafka: ERROR
    org.apache.kafka: ERROR