server:
  port: 8080  # 使用标准端口便于测试

# Spring相关配置
spring:
  application:
    name: galaxy-boot-playbook-service

  # 禁用数据库自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration

  # OkHttp相关配置
  cloud:
    openfeign:
      httpclient:
        maxConnections: 100
        timeToLive: 600
        timeToLiveUnit: SECONDS
      client:
        config:
          default:
            connectTimeout: 2000   # 单位：毫秒
            readTimeout: 10000     # 单位：毫秒
            writeTimeout: 10000     # 单位：毫秒
            loggerLevel: full
    loadbalancer:      # 需要loadbalancer时配置
      enabled: false   # 禁用负载均衡器
      retry:
        enabled: false

  # LocalCache的配置
  cache:
    type: caffeine
    caffeine:
      spec: > 
        maximumSize=500,
        expireAfterWrite=5s,
        recordStats

# Galaxy Boot相关配置
galaxy:
  system:
    code: CNF  # 系统三字码

  swagger:
    auth:
      username: admin
      password: admin@2015

  log:
    request-response:
      enabled: true            
      request-headers: true    
      response-headers: false  
      mask-field: false         
    performance:
      enabled: true            
    default-category: APP_LOG
    exception-pretty-print: true 

  metrics:
    datasource:
      prometheus:
        enable: false  # 禁用数据源监控

  security:
    input-protect: false   # 简化安全配置
    output-protect: false  
    actuator: 
      protect: false       

  # Galaxy AI 配置（保持原有配置用于对比测试）
  ai:
    enabled: true
    base-url: http://copilot.tsolph.chinastock.com.cn
    system-id: sys_cnf
    system-secret: VsJjm+PmoFn+VjF6-6bI1R9yU9ornxJ5
    account: shixiaolong_it
    app-id: app-5D55owR6HjNVPfCvsaYQKJ
    connect-timeout: 60000
    read-timeout: 120000   # 增加读取超时时间
    write-timeout: 60000

# Apollo相关配置（禁用）
apollo:
  bootstrap:
    enabled: false

# actuator & metrics相关配置
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: health,info
  health:
    defaults:
      enabled: false

# 日志级别相关配置
logging:
  level:
    root: INFO
    cn.com.chinastock.cnf.playbook: DEBUG
    cn.com.chinastock.cnf.playbook.service.ChatGlmService: DEBUG
    cn.com.chinastock.cnf.playbook.service.GalaxyAiService: DEBUG
    cn.com.chinastock.cnf.playbook.controller.ChatController: DEBUG
    okhttp3: DEBUG
    com.ctrip.framework.apollo: WARN
    org.springframework.kafka: ERROR
    org.apache.kafka: ERROR
    org.hibernate: ERROR
    org.springframework.orm: ERROR
