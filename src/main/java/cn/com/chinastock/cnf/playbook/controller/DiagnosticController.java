package cn.com.chinastock.cnf.playbook.controller;

import cn.com.chinastock.cnf.playbook.service.ChatGlmService;
import cn.com.chinastock.cnf.playbook.service.GalaxyAiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 诊断控制器
 * 用于检查各种服务的状态和配置
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@RestController
@RequestMapping("/v1/diagnostic")
public class DiagnosticController {

    private static final Logger logger = LoggerFactory.getLogger(DiagnosticController.class);

    @Autowired
    private GalaxyAiService galaxyAiService;

    @Autowired
    private ChatGlmService chatGlmService;

    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        logger.info("Performing system health check...");
        
        Map<String, Object> health = new HashMap<>();
        health.put("timestamp", System.currentTimeMillis());
        health.put("status", "UP");
        
        // 检查 ChatGLM 服务
        Map<String, Object> chatGlmHealth = new HashMap<>();
        try {
            boolean chatGlmHealthy = chatGlmService.testConnection();
            chatGlmHealth.put("status", chatGlmHealthy ? "UP" : "DOWN");
            chatGlmHealth.put("description", "ChatGLM API connection test");
        } catch (Exception e) {
            chatGlmHealth.put("status", "DOWN");
            chatGlmHealth.put("error", e.getMessage());
        }
        health.put("chatglm", chatGlmHealth);
        
        // 检查 Galaxy AI 服务
        Map<String, Object> galaxyHealth = new HashMap<>();
        try {
            // 这里可以添加 Galaxy AI 的健康检查逻辑
            galaxyHealth.put("status", "UNKNOWN");
            galaxyHealth.put("description", "Galaxy AI service status check not implemented");
        } catch (Exception e) {
            galaxyHealth.put("status", "DOWN");
            galaxyHealth.put("error", e.getMessage());
        }
        health.put("galaxy", galaxyHealth);
        
        return ResponseEntity.ok(health);
    }

    /**
     * 网络连接诊断
     */
    @GetMapping("/network")
    public ResponseEntity<Map<String, Object>> networkDiagnostic() {
        logger.info("Performing network diagnostic...");
        
        Map<String, Object> diagnostic = new HashMap<>();
        diagnostic.put("timestamp", System.currentTimeMillis());
        
        // 测试外网连接
        Map<String, Object> externalConnectivity = new HashMap<>();
        try {
            // 测试 ChatGLM API 连接
            boolean chatGlmReachable = chatGlmService.testConnection();
            externalConnectivity.put("chatglm_api", chatGlmReachable ? "REACHABLE" : "UNREACHABLE");
        } catch (Exception e) {
            externalConnectivity.put("chatglm_api", "ERROR: " + e.getMessage());
        }
        
        diagnostic.put("external_connectivity", externalConnectivity);
        
        // 系统信息
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("java_version", System.getProperty("java.version"));
        systemInfo.put("os_name", System.getProperty("os.name"));
        systemInfo.put("available_processors", Runtime.getRuntime().availableProcessors());
        systemInfo.put("max_memory", Runtime.getRuntime().maxMemory());
        systemInfo.put("free_memory", Runtime.getRuntime().freeMemory());
        
        diagnostic.put("system_info", systemInfo);
        
        return ResponseEntity.ok(diagnostic);
    }

    /**
     * SSE 配置诊断
     */
    @GetMapping("/sse-config")
    public ResponseEntity<Map<String, Object>> sseConfigDiagnostic() {
        logger.info("Performing SSE configuration diagnostic...");
        
        Map<String, Object> diagnostic = new HashMap<>();
        diagnostic.put("timestamp", System.currentTimeMillis());
        
        // HTTP 客户端配置信息
        Map<String, Object> httpConfig = new HashMap<>();
        httpConfig.put("okhttp_version", "检查 OkHttp 版本");
        httpConfig.put("sse_support", "EventSource 支持检查");
        
        // 推荐的超时配置
        Map<String, Object> recommendedTimeouts = new HashMap<>();
        recommendedTimeouts.put("connect_timeout", "30 seconds");
        recommendedTimeouts.put("read_timeout", "120 seconds (for streaming)");
        recommendedTimeouts.put("write_timeout", "30 seconds");
        
        httpConfig.put("recommended_timeouts", recommendedTimeouts);
        
        // SSE 最佳实践
        Map<String, Object> sseBestPractices = new HashMap<>();
        sseBestPractices.put("content_type", "text/event-stream");
        sseBestPractices.put("cache_control", "no-cache");
        sseBestPractices.put("connection", "keep-alive");
        sseBestPractices.put("event_format", "data: {json}\\n\\n");
        sseBestPractices.put("end_marker", "[DONE]");
        
        diagnostic.put("http_config", httpConfig);
        diagnostic.put("sse_best_practices", sseBestPractices);
        
        // 常见问题检查清单
        Map<String, Object> troubleshooting = new HashMap<>();
        troubleshooting.put("check_accept_header", "Accept: text/event-stream");
        troubleshooting.put("check_stream_parameter", "stream: true in request body");
        troubleshooting.put("check_timeout_settings", "Ensure read timeout is sufficient for streaming");
        troubleshooting.put("check_proxy_settings", "Proxy may interfere with SSE connections");
        troubleshooting.put("check_firewall", "Firewall may block streaming connections");
        
        diagnostic.put("troubleshooting_checklist", troubleshooting);
        
        return ResponseEntity.ok(diagnostic);
    }

    /**
     * 比较 ChatGLM 和 Galaxy AI 的配置差异
     */
    @GetMapping("/compare-services")
    public ResponseEntity<Map<String, Object>> compareServices() {
        logger.info("Comparing ChatGLM and Galaxy AI service configurations...");
        
        Map<String, Object> comparison = new HashMap<>();
        comparison.put("timestamp", System.currentTimeMillis());
        
        // ChatGLM 配置
        Map<String, Object> chatGlmConfig = new HashMap<>();
        chatGlmConfig.put("base_url", "https://open.bigmodel.cn/api/paas/v4");
        chatGlmConfig.put("model", "glm-4-air");
        chatGlmConfig.put("auth_method", "Bearer token");
        chatGlmConfig.put("response_format", "OpenAI compatible");
        chatGlmConfig.put("sse_support", "Native");
        
        // Galaxy AI 配置（从代码中推断）
        Map<String, Object> galaxyConfig = new HashMap<>();
        galaxyConfig.put("base_url", "Internal private model");
        galaxyConfig.put("model", "Custom model");
        galaxyConfig.put("auth_method", "Custom token system");
        galaxyConfig.put("response_format", "Custom format converted to OpenAI");
        galaxyConfig.put("sse_support", "Custom implementation");
        
        comparison.put("chatglm", chatGlmConfig);
        comparison.put("galaxy_ai", galaxyConfig);
        
        // 关键差异点
        Map<String, Object> keyDifferences = new HashMap<>();
        keyDifferences.put("authentication", "ChatGLM uses simple Bearer token, Galaxy AI uses complex token system");
        keyDifferences.put("response_processing", "ChatGLM returns OpenAI format directly, Galaxy AI needs conversion");
        keyDifferences.put("error_handling", "Different error response formats");
        keyDifferences.put("timeout_sensitivity", "Private models may be slower than public APIs");
        
        comparison.put("key_differences", keyDifferences);
        
        return ResponseEntity.ok(comparison);
    }
}
