package cn.com.chinastock.cnf.playbook.dto;

import cn.com.chinastock.cnf.core.log.aspect.MaskedField;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @create 2025/1/10 14:52
 */
@Schema(description = "掩码测试对象")
public class MaskDTO {

    @Schema(description = "账号", example = "12345678", minLength = 1, maxLength = 10)
    private String acctNo;

    @Schema(description = "掩码字段", example = "abc", minLength = 1, maxLength = 1024)
    @MaskedField
    private String mask;


    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public String getMask() {
        return mask;
    }

    public void setMask(String mask) {
        this.mask = mask;
    }

    @Override
    public String toString() {
        return "MaskDTO{" +
                "acctNo='" + acctNo + '\'' +
                ", mask='" + mask + '\'' +
                '}';
    }
}
