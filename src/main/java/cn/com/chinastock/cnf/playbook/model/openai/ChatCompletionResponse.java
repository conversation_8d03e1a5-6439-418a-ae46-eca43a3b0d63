package cn.com.chinastock.cnf.playbook.model.openai;

import com.alibaba.fastjson2.annotation.JSONField;

import java.util.List;

/**
 * OpenAI Chat Completion 响应模型
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class ChatCompletionResponse {
    
    /**
     * 响应 ID
     */
    @JSONField(name = "id")
    private String id;
    
    /**
     * 对象类型
     */
    @JSONField(name = "object")
    private String object = "chat.completion";
    
    /**
     * 创建时间戳
     */
    @JSONField(name = "created")
    private Long created;
    
    /**
     * 模型名称
     */
    @JSONField(name = "model")
    private String model;
    
    /**
     * 选择列表
     */
    @JSONField(name = "choices")
    private List<Choice> choices;
    
    /**
     * 使用情况
     */
    @JSONField(name = "usage")
    private Usage usage;
    
    /**
     * 系统指纹
     */
    @JSONField(name = "system_fingerprint")
    private String systemFingerprint;
    
    public ChatCompletionResponse() {
        this.created = System.currentTimeMillis() / 1000;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getObject() {
        return object;
    }
    
    public void setObject(String object) {
        this.object = object;
    }
    
    public Long getCreated() {
        return created;
    }
    
    public void setCreated(Long created) {
        this.created = created;
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public List<Choice> getChoices() {
        return choices;
    }
    
    public void setChoices(List<Choice> choices) {
        this.choices = choices;
    }
    
    public Usage getUsage() {
        return usage;
    }
    
    public void setUsage(Usage usage) {
        this.usage = usage;
    }
    
    public String getSystemFingerprint() {
        return systemFingerprint;
    }
    
    public void setSystemFingerprint(String systemFingerprint) {
        this.systemFingerprint = systemFingerprint;
    }
    
    /**
     * 选择项
     */
    public static class Choice {
        @JSONField(name = "index")
        private Integer index;
        
        @JSONField(name = "message")
        private ChatMessage message;
        
        @JSONField(name = "finish_reason")
        private String finishReason;
        
        public Choice() {
        }
        
        public Choice(Integer index, ChatMessage message, String finishReason) {
            this.index = index;
            this.message = message;
            this.finishReason = finishReason;
        }
        
        public Integer getIndex() {
            return index;
        }
        
        public void setIndex(Integer index) {
            this.index = index;
        }
        
        public ChatMessage getMessage() {
            return message;
        }
        
        public void setMessage(ChatMessage message) {
            this.message = message;
        }
        
        public String getFinishReason() {
            return finishReason;
        }
        
        public void setFinishReason(String finishReason) {
            this.finishReason = finishReason;
        }
    }
    
    /**
     * 使用情况
     */
    public static class Usage {
        @JSONField(name = "prompt_tokens")
        private Integer promptTokens;
        
        @JSONField(name = "completion_tokens")
        private Integer completionTokens;
        
        @JSONField(name = "total_tokens")
        private Integer totalTokens;
        
        public Usage() {
        }
        
        public Usage(Integer promptTokens, Integer completionTokens, Integer totalTokens) {
            this.promptTokens = promptTokens;
            this.completionTokens = completionTokens;
            this.totalTokens = totalTokens;
        }
        
        public Integer getPromptTokens() {
            return promptTokens;
        }
        
        public void setPromptTokens(Integer promptTokens) {
            this.promptTokens = promptTokens;
        }
        
        public Integer getCompletionTokens() {
            return completionTokens;
        }
        
        public void setCompletionTokens(Integer completionTokens) {
            this.completionTokens = completionTokens;
        }
        
        public Integer getTotalTokens() {
            return totalTokens;
        }
        
        public void setTotalTokens(Integer totalTokens) {
            this.totalTokens = totalTokens;
        }
    }
}
