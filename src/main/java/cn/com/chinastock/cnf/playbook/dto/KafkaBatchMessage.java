package cn.com.chinastock.cnf.playbook.dto;

import java.util.List;

/**
 * 批量消息发送请求DTO
 */
public class KafkaBatchMessage {
    
    private String topic;
    private List<MessageItem> messages;
    
    public KafkaBatchMessage() {
    }
    
    public KafkaBatchMessage(String topic, List<MessageItem> messages) {
        this.topic = topic;
        this.messages = messages;
    }
    
    public String getTopic() {
        return topic;
    }
    
    public void setTopic(String topic) {
        this.topic = topic;
    }
    
    public List<MessageItem> getMessages() {
        return messages;
    }
    
    public void setMessages(List<MessageItem> messages) {
        this.messages = messages;
    }
    
    @Override
    public String toString() {
        return "BatchMessageRequest{" +
                "topic='" + topic + '\'' +
                ", messages=" + messages +
                '}';
    }
    
    public static class MessageItem {
        private String key;
        private String message;
        
        public MessageItem() {
        }
        
        public MessageItem(String key, String message) {
            this.key = key;
            this.message = message;
        }
        
        public String getKey() {
            return key;
        }
        
        public void setKey(String key) {
            this.key = key;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        @Override
        public String toString() {
            return "MessageItem{" +
                    "key='" + key + '\'' +
                    ", message='" + message + '\'' +
                    '}';
        }
    }
}
