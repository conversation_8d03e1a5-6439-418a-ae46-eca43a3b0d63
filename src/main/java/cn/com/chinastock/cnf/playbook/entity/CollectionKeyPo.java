package cn.com.chinastock.cnf.playbook.entity;


import java.io.Serializable;
import java.util.Objects;

public class CollectionKeyPo  implements Serializable {
    private String hashCode;

    private String busiType;

    private String type;

    private String sysCode;

    private String dataId;

    private String dataType;

    public String getHashCode() {
        return hashCode;
    }

    public void setHashCode(String hashCode) {
        this.hashCode = hashCode;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String sysCode) {
        this.sysCode = sysCode;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        CollectionKeyPo that = (CollectionKeyPo) o;
        return Objects.equals(hashCode, that.hashCode) && Objects.equals(busiType, that.busiType) && Objects.equals(type, that.type) && Objects.equals(sysCode, that.sysCode) && Objects.equals(dataId, that.dataId) && Objects.equals(dataType, that.dataType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(hashCode, busiType, type, sysCode, dataId, dataType);
    }
}
