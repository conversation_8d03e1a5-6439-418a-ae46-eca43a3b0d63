package cn.com.chinastock.cnf.playbook.client;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = "exception-service", url = "http://localhost:8088")
public interface ExceptionClient {

    @GetMapping("/api/test/exception/forbidden")
    BaseResponse<Object> forbidden();

    @GetMapping("/api/test/exception/not-found")
    BaseResponse<Object> notFound();

    @GetMapping("/api/test/esb/timeout")
    String timeout();
}
