package cn.com.chinastock.cnf.playbook.dto;

import cn.com.chinastock.cnf.core.log.aspect.MaskedField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

public class LogDTO {

    @MaskedField
    private String maskData;

    @Schema(description = "数据内容", minimum = "1", maximum = "1024")
    @NotBlank(message="数据内容不能为空")
    private String data;

    @Pattern(regexp="^([1][0-9]{8,10})|\\s*$",message="手机号码格式不正确")
    private String mob;

    public LogDTO() {
    }

    public LogDTO(String data) {
        this.data = data;
    }

    public LogDTO(String data, String maskData) {
        this.data = data;
        this.maskData = maskData;
    }

    @Override
    public String toString() {
        return "LogDTO{" +
                "maskData='" + maskData + '\'' +
                ", data='" + data + '\'' +
                ", mob='" + mob + '\'' +
                '}';
    }


    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getMaskData() {
        return maskData;
    }

    public void setMaskData(String maskData) {
        this.maskData = maskData;
    }

    public String getMob() {
        return mob;
    }

    public void setMob(String mob) {
        this.mob = mob;
    }
}
