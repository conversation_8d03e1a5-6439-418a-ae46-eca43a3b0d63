package cn.com.chinastock.cnf.playbook.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.playbook.entity.CollectionPo;
import cn.com.chinastock.cnf.playbook.repository.ICollectionDbRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/collection")
public class CollectionController {

    @Autowired
    private ICollectionDbRepo iCollectionDbRepo;

    @PostMapping("/queryCollection")
    public BaseResponse<CollectionPo> queryCollection(@RequestBody CollectionPo collectionPo){
        CollectionPo po = iCollectionDbRepo.findByHashCodeAndBusiTypeAndTypeAndSysCodeAndDataIdAndDataType(collectionPo.getHashCode(), collectionPo.getBusiType(), collectionPo.getType(), collectionPo.getSysCode(), collectionPo.getDataId(), collectionPo.getDataType());
        return new BaseResponse<>(new Meta(true, "0", "success"), po);
    }
}
