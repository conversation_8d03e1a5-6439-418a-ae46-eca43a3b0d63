package cn.com.chinastock.cnf.playbook.controller;

import cn.com.chinastock.cnf.core.exception.GalaxyFeignException;
import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.playbook.client.BdpClient;
import cn.com.chinastock.cnf.playbook.client.ESBClient;
import cn.com.chinastock.cnf.playbook.client.ExceptionClient;
import cn.com.chinastock.cnf.playbook.client.LoadBalancerClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test/feign")
public class FeignController {

    private final ExceptionClient exceptionClient;

    private final ESBClient eSBClient;

    private final LoadBalancerClient loadBalancerClient;

    private final BdpClient bdpClient;

    public FeignController(ExceptionClient exceptionClient, ESBClient eSBClient, LoadBalancerClient loadBalancerClient, BdpClient bdpClient) {
        this.exceptionClient = exceptionClient;
        this.eSBClient = eSBClient;
        this.loadBalancerClient = loadBalancerClient;
        this.bdpClient = bdpClient;
    }

    @GetMapping("/timeout")
    public BaseResponse<Object> timeout() {
        exceptionClient.timeout();
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }

    @GetMapping("/esb")
    public BaseResponse<Object> callESB(@RequestParam(value = "serviceNo") String serviceNo) {
        Map<String, String> req = new HashMap<>();
        req.put("serviceNo", serviceNo);
        req.put("sysCode", "MPA");
        return eSBClient.queryServiceNo(req);
    }

    @GetMapping("/feign-exception")
    public BaseResponse<Object> callNotFoundAPI() {
        try {
            exceptionClient.notFound();
        } catch (GalaxyFeignException e) {
            GalaxyLogger.error("GalaxyFeignException, code={}, message={}", e.getCode(), e.getMessage());
            return new BaseResponse<>(e.getMeta(), null);
        }
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }

    @GetMapping("/galaxy-exception")
    public BaseResponse<Object> callException() {
        try {
            exceptionClient.forbidden();
        } catch (GalaxyFeignException e) {
            GalaxyLogger.error("GalaxyFeignException, code={}, message={}", e.getCode(), e.getMessage());
            return new BaseResponse<>(e.getMeta(), null);
        }
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }

    @GetMapping("/loadbalancer")
    public BaseResponse<Object> loadbalancer() {
        loadBalancerClient.headers();
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }

    @GetMapping("/bdp/queryMyAssetDetail")
    public BaseResponse<Object> queryMyAssetDetail(@RequestParam(value = "custNo") String custNo) {
        Map<String, String> req = new HashMap<>();
        req.put("CustNo", custNo);
        return bdpClient.queryMyAssetDetail(req);
    }
}
