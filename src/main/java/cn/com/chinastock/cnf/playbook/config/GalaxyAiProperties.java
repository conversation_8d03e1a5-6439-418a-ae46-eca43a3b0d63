package cn.com.chinastock.cnf.playbook.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Galaxy AI 服务配置属性
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@Component
@ConfigurationProperties(prefix = "galaxy.ai")
public class GalaxyAiProperties {
    
    /**
     * Galaxy AI 服务基础 URL
     */
    private String baseUrl = "http://copilot.prodgpu.chinastock.com.cn";
    
    /**
     * 系统 ID
     */
    private String systemId;
    
    /**
     * 系统密钥
     */
    private String systemSecret;
    
    /**
     * 账户
     */
    private String account;
    
    /**
     * 应用 ID
     */
    private String appId;
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 10000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;
    
    /**
     * 写入超时时间（毫秒）
     */
    private int writeTimeout = 60000;
    
    /**
     * Token 有效期（毫秒），默认 7 天
     */
    private long tokenValidityPeriod = 7 * 24 * 60 * 60 * 1000L;
    
    /**
     * 是否启用 Galaxy AI 服务
     */
    private boolean enabled = true;
    
    /**
     * 默认模型名称
     */
    private String defaultModel = "galaxy-ai";
    
    public String getBaseUrl() {
        return baseUrl;
    }
    
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    public String getSystemId() {
        return systemId;
    }
    
    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }
    
    public String getSystemSecret() {
        return systemSecret;
    }
    
    public void setSystemSecret(String systemSecret) {
        this.systemSecret = systemSecret;
    }
    
    public String getAccount() {
        return account;
    }
    
    public void setAccount(String account) {
        this.account = account;
    }
    
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public int getConnectTimeout() {
        return connectTimeout;
    }
    
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
    
    public int getReadTimeout() {
        return readTimeout;
    }
    
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    
    public int getWriteTimeout() {
        return writeTimeout;
    }
    
    public void setWriteTimeout(int writeTimeout) {
        this.writeTimeout = writeTimeout;
    }
    
    public long getTokenValidityPeriod() {
        return tokenValidityPeriod;
    }
    
    public void setTokenValidityPeriod(long tokenValidityPeriod) {
        this.tokenValidityPeriod = tokenValidityPeriod;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getDefaultModel() {
        return defaultModel;
    }
    
    public void setDefaultModel(String defaultModel) {
        this.defaultModel = defaultModel;
    }
    
    /**
     * 验证必要的配置项
     */
    public void validate() {
        if (!enabled) {
            return;
        }
        
        if (systemId == null || systemId.trim().isEmpty()) {
            throw new IllegalArgumentException("galaxy.ai.system-id is required when Galaxy AI is enabled");
        }
        
        if (systemSecret == null || systemSecret.trim().isEmpty()) {
            throw new IllegalArgumentException("galaxy.ai.system-secret is required when Galaxy AI is enabled");
        }
        
        if (account == null || account.trim().isEmpty()) {
            throw new IllegalArgumentException("galaxy.ai.account is required when Galaxy AI is enabled");
        }
        
        if (appId == null || appId.trim().isEmpty()) {
            throw new IllegalArgumentException("galaxy.ai.app-id is required when Galaxy AI is enabled");
        }
    }
}
