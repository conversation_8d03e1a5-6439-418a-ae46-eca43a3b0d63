package cn.com.chinastock.cnf.playbook.controller;

import cn.com.chinastock.cnf.playbook.dto.CacheProduct;
import cn.com.chinastock.cnf.playbook.service.CacheProductService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test/cache")
public class CacheController {

    private final CacheProductService cacheProductService;

    public CacheController(CacheProductService cacheProductService) {
        this.cacheProductService = cacheProductService;
    }

    @GetMapping("/products/{id}")
    public CacheProduct getProduct(@PathVariable String id) {
        return cacheProductService.getProductById(id);
    }
}