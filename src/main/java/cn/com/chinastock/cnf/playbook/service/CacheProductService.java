package cn.com.chinastock.cnf.playbook.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.playbook.dto.CacheProduct;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
public class CacheProductService {

    /**
     * MVC模式：返回普通对象，使用短期缓存
     * 
     * @param id 产品ID
     * @return 产品信息对象
     */
    @Cacheable(cacheNames = "shortTermCache", key = "#id")
    public CacheProduct getProductById(String id) {
        GalaxyLogger.info("【MVC】正在从数据库查询产品，ID: {}", id);
        // 模拟耗时操作
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return new CacheProduct(id, "Product " + id, "Description for MVC product.");
    }
}