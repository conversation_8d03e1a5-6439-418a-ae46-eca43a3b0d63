package cn.com.chinastock.cnf.playbook.repository;

import cn.com.chinastock.cnf.playbook.entity.CollectionPo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface ICollectionDbRepo extends JpaRepository<CollectionPo,String>, JpaSpecificationExecutor<CollectionPo> {
    CollectionPo findByHashCodeAndBusiTypeAndTypeAndSysCodeAndDataIdAndDataType(String hashCode, String busiType, String type, String sysCode, String dataId, String dataType);
}

