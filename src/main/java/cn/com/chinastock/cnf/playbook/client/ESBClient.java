package cn.com.chinastock.cnf.playbook.client;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

@FeignClient(name = "esb-service", url = "${esb.url:http://10.4.27.52:22112/apiJson/V2/ctc}")
public interface ESBClient {

    @PostMapping(value = "/", headers = {"Function-No=YH0019010400001"})
    BaseResponse<Object> queryServiceNo(Map req);

}
