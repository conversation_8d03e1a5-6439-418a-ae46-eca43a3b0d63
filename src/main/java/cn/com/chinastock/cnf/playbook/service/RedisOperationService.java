package cn.com.chinastock.cnf.playbook.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.playbook.dto.RedisTestResult;
import cn.com.chinastock.cnf.playbook.dto.RedisTestUser;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;

/**
 * Redis 操作测试服务
 *
 * <AUTHOR>
 */
@Service
public class RedisOperationService {

    private static final String KEY_PREFIX = "CNF:";
    private final RedisTemplate<String, Object> redisTemplate;

    public RedisOperationService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 测试 String 操作.
     *
     * @return 测试结果
     */
    public RedisTestResult testStringOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String keyPrefix = KEY_PREFIX + "test:string:" + System.currentTimeMillis() + ":";

            // SET 操作
            String key = keyPrefix + "simple";
            String valueToSet = "Hello Redis";
            GalaxyLogger.info("testStringOperations, Set: {} = {}", key, valueToSet);
            redisTemplate.opsForValue().set(key, valueToSet);

            // GET 操作
            GalaxyLogger.info("testStringOperations, Get: {}", key);
            String value = (String) redisTemplate.opsForValue().get(key);

            // SETEX 操作（带过期时间）
            GalaxyLogger.info("testStringOperations, SetEX: {} = {} (10 seconds)", keyPrefix + "expire", "Will expire");
            redisTemplate.opsForValue().set(keyPrefix + "expire", "Will expire", 10, TimeUnit.SECONDS);

            // TTL 操作
            GalaxyLogger.info("testStringOperations, getExpire: {}", keyPrefix + "expire");
            Long ttl = redisTemplate.getExpire(keyPrefix + "expire", TimeUnit.SECONDS);

            // INCR 操作
            GalaxyLogger.info("testStringOperations, Set: {} = 0", keyPrefix + "counter");
            redisTemplate.opsForValue().set(keyPrefix + "counter", 0);

            GalaxyLogger.info("testStringOperations, INCR: {}", keyPrefix + "counter");
            Long counter = redisTemplate.opsForValue().increment(keyPrefix + "counter");

            long executionTime = System.currentTimeMillis() - startTime;


            Map<String, Object> data = new HashMap<>();
            data.put("getValue", value);
            data.put("ttl", ttl);
            data.put("counter", counter);

            RedisTestResult result = RedisTestResult.success("String 操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            GalaxyLogger.error("String 操作测试失败", e);
            return RedisTestResult.failure("String 操作测试失败", e);
        }
    }

    /**
     * 测试 Hash 操作.
     *
     * @return 测试结果
     */
    public RedisTestResult testHashOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String key = KEY_PREFIX + "test:hash:" + System.currentTimeMillis();

            // HSET 操作
            GalaxyLogger.info("testHashOperations, HSET: {} field={} value={}", key, "field1", "value1");
            redisTemplate.opsForHash().put(key, "field1", "value1");
            GalaxyLogger.info("testHashOperations, HSET: {} field={} value={}", key, "field2", "value2");
            redisTemplate.opsForHash().put(key, "field2", "value2");

            // HGET 操作
            GalaxyLogger.info("testHashOperations, HGET: {} field={}", key, "field1");
            Object field1Value = redisTemplate.opsForHash().get(key, "field1");

            // HGETALL 操作
            GalaxyLogger.info("testHashOperations, HGETALL: {}", key);
            Map<Object, Object> allFields = redisTemplate.opsForHash().entries(key);

            // HDEL 操作
            GalaxyLogger.info("testHashOperations, HDEL: {} field={}", key, "field2");
            redisTemplate.opsForHash().delete(key, "field2");

            // 验证删除
            GalaxyLogger.info("testHashOperations, HASKEY: {} field={}", key, "field2");
            Boolean field2Exists = redisTemplate.opsForHash().hasKey(key, "field2");

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = new HashMap<>();
            data.put("field1Value", field1Value);
            data.put("allFieldsCount", allFields.size());
            data.put("field2Exists", field2Exists);

            RedisTestResult result = RedisTestResult.success("Hash 操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return RedisTestResult.failure("Hash 操作测试失败", e);
        }
    }

    /**
     * 测试 List 操作.
     *
     * @return 测试结果
     */
    public RedisTestResult testListOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String key = KEY_PREFIX + "test:list:" + System.currentTimeMillis();

            // LPUSH 操作
            GalaxyLogger.info("testListOperations, LPUSH: {} value={}", key, "item1");
            redisTemplate.opsForList().leftPush(key, "item1");
            GalaxyLogger.info("testListOperations, LPUSH: {} value={}", key, "item2");
            redisTemplate.opsForList().leftPush(key, "item2");

            // RPUSH 操作
            GalaxyLogger.info("testListOperations, RPUSH: {} value={}", key, "item3");
            redisTemplate.opsForList().rightPush(key, "item3");

            // LRANGE 操作
            GalaxyLogger.info("testListOperations, LRANGE: {} start=0 end=-1", key);
            List<Object> allItems = redisTemplate.opsForList().range(key, 0, -1);

            // LPOP 操作
            GalaxyLogger.info("testListOperations, LPOP: {}", key);
            Object leftPoppedItem = redisTemplate.opsForList().leftPop(key);

            // 获取列表长度
            GalaxyLogger.info("testListOperations, SIZE: {}", key);
            Long listSize = redisTemplate.opsForList().size(key);

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = new HashMap<>();
            data.put("allItems", allItems);
            data.put("leftPoppedItem", leftPoppedItem);
            data.put("finalSize", listSize);

            RedisTestResult result = RedisTestResult.success("List 操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return RedisTestResult.failure("List 操作测试失败", e);
        }
    }

    /**
     * 测试 Set 操作.
     *
     * @return 测试结果
     */
    public RedisTestResult testSetOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String key = KEY_PREFIX + "test:set:" + System.currentTimeMillis();

            // SADD 操作
            GalaxyLogger.info("testSetOperations, SADD: {} members={}", key, Arrays.asList("member1", "member2", "member3"));
            redisTemplate.opsForSet().add(key, "member1", "member2", "member3");

            // SMEMBERS 操作
            GalaxyLogger.info("testSetOperations, SMEMBERS: {}", key);
            Set<Object> allMembers = redisTemplate.opsForSet().members(key);

            // SISMEMBER 操作
            GalaxyLogger.info("testSetOperations, SISMEMBER: {} member={}", key, "member1");
            Boolean isMember = redisTemplate.opsForSet().isMember(key, "member1");

            // SREM 操作
            GalaxyLogger.info("testSetOperations, SREM: {} member={}", key, "member2");
            redisTemplate.opsForSet().remove(key, "member2");

            // 获取集合大小
            GalaxyLogger.info("testSetOperations, SIZE: {}", key);
            Long setSize = redisTemplate.opsForSet().size(key);

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = new HashMap<>();
            data.put("allMembers", allMembers);
            data.put("isMember1", isMember);
            data.put("finalSize", setSize);

            RedisTestResult result = RedisTestResult.success("Set 操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return RedisTestResult.failure("Set 操作测试失败", e);
        }
    }

    /**
     * 测试 ZSet 操作.
     *
     * @return 测试结果
     */
    public RedisTestResult testZSetOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String key = KEY_PREFIX + "test:zset:" + System.currentTimeMillis();

            // ZADD 操作
            GalaxyLogger.info("testZSetOperations, ZADD: {} member={} score={}", key, "member1", 1.0);
            redisTemplate.opsForZSet().add(key, "member1", 1.0);
            GalaxyLogger.info("testZSetOperations, ZADD: {} member={} score={}", key, "member2", 2.0);
            redisTemplate.opsForZSet().add(key, "member2", 2.0);
            GalaxyLogger.info("testZSetOperations, ZADD: {} member={} score={}", key, "member3", 3.0);
            redisTemplate.opsForZSet().add(key, "member3", 3.0);

            // ZRANGE 操作
            GalaxyLogger.info("testZSetOperations, ZRANGE: {} start=0 end=-1", key);
            Set<Object> rangeMembers = redisTemplate.opsForZSet().range(key, 0, -1);

            // ZSCORE 操作
            GalaxyLogger.info("testZSetOperations, ZSCORE: {} member={}", key, "member2");
            Double score = redisTemplate.opsForZSet().score(key, "member2");

            // ZREM 操作
            GalaxyLogger.info("testZSetOperations, ZREM: {} member={}", key, "member1");
            redisTemplate.opsForZSet().remove(key, "member1");

            // 获取有序集合大小
            GalaxyLogger.info("testZSetOperations, SIZE: {}", key);
            Long zsetSize = redisTemplate.opsForZSet().size(key);

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = new HashMap<>();
            data.put("rangeMembers", rangeMembers);
            data.put("member2Score", score);
            data.put("finalSize", zsetSize);

            RedisTestResult result = RedisTestResult.success("ZSet 操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return RedisTestResult.failure("ZSet 操作测试失败", e);
        }
    }

    /**
     * 测试过期时间操作.
     *
     * @return 测试结果
     */
    public RedisTestResult testExpirationOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String key = KEY_PREFIX + "test:expiration:" + System.currentTimeMillis();

            // 设置带过期时间的键
            GalaxyLogger.info("testExpirationOperations, SET: {} value={} expire={}s", key, "expiring value", 5);
            redisTemplate.opsForValue().set(key, "expiring value", 5, TimeUnit.SECONDS);

            // 检查 TTL
            GalaxyLogger.info("testExpirationOperations, TTL: {}", key);
            Long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);

            // 延长过期时间
            GalaxyLogger.info("testExpirationOperations, EXPIRE: {} newExpire={}s", key, 10);
            redisTemplate.expire(key, 10, TimeUnit.SECONDS);
            Long newTtl = redisTemplate.getExpire(key, TimeUnit.SECONDS);

            // 移除过期时间
            GalaxyLogger.info("testExpirationOperations, PERSIST: {}", key);
            redisTemplate.persist(key);
            Long persistTtl = redisTemplate.getExpire(key, TimeUnit.SECONDS);

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = Map.of(
                    "initialTtl", ttl,
                    "extendedTtl", newTtl,
                    "persistTtl", persistTtl
            );

            RedisTestResult result = RedisTestResult.success("过期时间操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return RedisTestResult.failure("过期时间操作测试失败", e);
        }
    }

    /**
     * 批量写入并批量读取String键值对测试.
     *
     * @return 测试结果
     */
    public RedisTestResult testMultiSetAndGetOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String keyPrefix = KEY_PREFIX + "test:multiSetAndGet:" + System.currentTimeMillis() + ":";
            Map<String, String> kvMap = new HashMap<>();
            for (int i = 0; i < 5; i++) {
                kvMap.put(keyPrefix + i, "value" + i);
            }
            GalaxyLogger.info("testMultiSetAndGetOperations, MSET: {}", kvMap);
            redisTemplate.opsForValue().multiSet(kvMap);
            // 批量读取
            GalaxyLogger.info("testMultiSetAndGetOperations, MGET: {}", kvMap.keySet());
            List<String> values = (List<String>) (List<?>) redisTemplate.opsForValue().multiGet(new ArrayList<>(kvMap.keySet()));
            long executionTime = System.currentTimeMillis() - startTime;
            Map<String, Object> data = new HashMap<>();
            data.put("multiSetKeys", kvMap.keySet());
            data.put("multiSetValues", kvMap.values());
            data.put("multiGetValues", values);
            RedisTestResult result = RedisTestResult.success("批量写入并读取成功", data);
            result.setExecutionTime(executionTime);
            return result;
        } catch (Exception e) {
            GalaxyLogger.error("批量写入并读取失败", e);
            return RedisTestResult.failure("批量写入并读取失败", e);
        }
    }

    /**
     * 运行所有 Redis 操作测试.
     *
     * @return 所有Redis操作测试结果列表
     */
    public List<RedisTestResult> runAllRedisOperationTests() {
        List<RedisTestResult> results = new ArrayList<>();
        results.add(testStringOperations());
        results.add(testHashOperations());
        results.add(testListOperations());
        results.add(testSetOperations());
        results.add(testZSetOperations());
        results.add(testMultiSetAndGetOperations());
        results.add(testObjectSerialization());
        return results;
    }

    /**
     * 测试对象序列化器（默认使用FastJson2）.
     *
     * @return 测试结果
     */
    public RedisTestResult testObjectSerialization() {

        try {
            long startTime = System.currentTimeMillis();

            // 创建测试用户
            RedisTestUser user = new RedisTestUser(1L, "张三", "<EMAIL>");
            user.setTags(Arrays.asList("developer", "java", "redis"));
            user.setMetadata(Map.of("department", "IT", "level", "senior"));
            String key = KEY_PREFIX + "test:serialization:default:" + System.currentTimeMillis();

            // 存储对象
            GalaxyLogger.info("testObjectSerialization, SET: {}, value={}", key, user);
            redisTemplate.opsForValue().set(key, user, 500, TimeUnit.MILLISECONDS);

            // 读取对象
            GalaxyLogger.info("testObjectSerialization, GET: {}", key);
            RedisTestUser retrievedUser = (RedisTestUser) redisTemplate.opsForValue().get(key);

            // 验证数据完整性
            boolean isValid = validateUser(user, retrievedUser);

            long executionTime = System.currentTimeMillis() - startTime;

            if (isValid) {
                RedisTestResult result = RedisTestResult.success("默认序列化器测试成功", retrievedUser);
                result.setExecutionTime(executionTime);
                return result;
            } else {
                return RedisTestResult.failure("序列化后数据不一致");
            }

        } catch (Exception e) {
            return RedisTestResult.failure("默认序列化器测试失败", e);
        }
    }

    private boolean validateUser(RedisTestUser original, RedisTestUser retrieved) {
        if (retrieved == null) return false;
        return Objects.equals(original.getId(), retrieved.getId()) &&
                Objects.equals(original.getName(), retrieved.getName()) &&
                Objects.equals(original.getEmail(), retrieved.getEmail());
    }
}
