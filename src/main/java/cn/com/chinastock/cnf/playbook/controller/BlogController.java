package cn.com.chinastock.cnf.playbook.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.playbook.entity.BlogPo;
import cn.com.chinastock.cnf.playbook.repository.IBlogRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/api/blog")
public class BlogController {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private IBlogRepo blogRepo;

    @PostMapping("/")
    public BaseResponse<BlogPo> autoCreate() {
        BlogPo blog = new BlogPo();
        blog.setTitle("title-" + System.currentTimeMillis());
        blog.setContent("content-" + System.currentTimeMillis());
        blog.setAuthor("author-" + System.currentTimeMillis());
        blogRepo.save(blog);
        return new BaseResponse<>(new Meta(true, "0", "success"), blog);
    }

    @GetMapping("/id")
    public BaseResponse<BlogPo> queryById(@RequestParam(name = "id") Long id) {
        Optional<BlogPo> blog = blogRepo.findById(id);
        return new BaseResponse<>(new Meta(true, "0", "success"), blog.orElse(null));
    }

    @PostMapping("/batch")
    public BaseResponse<List<BlogPo>> batchSave(@RequestParam(name = "batchSize", defaultValue = "10000") int batchSize,
                               @RequestParam(name = "batchCount", defaultValue = "10") int batchCount) {
        List<BlogPo> createdBlogs = new ArrayList<>();
        List<CompletableFuture<List<BlogPo>>> futures = new ArrayList<>();

        for (int batch = 0; batch < batchCount; batch++) {
            int finalBatch = batch;
            CompletableFuture<List<BlogPo>> future = CompletableFuture.supplyAsync(() -> {
                List<BlogPo> batchBlogs = new ArrayList<>();
                try {
                    for (int i = 0; i < batchSize; i++) {
                        // 先执行查询操作
                        jdbcTemplate.queryForList("SELECT COUNT(*) FROM blog");

                        String sql = "SELECT blog_sequence.NEXTVAL FROM DUAL";
                        Long id = jdbcTemplate.queryForObject(sql, Long.class);

                        // 创建并插入新博客
                        BlogPo blog = new BlogPo();
                        blog.setId(id);
                        blog.setTitle("title-" + finalBatch + "-" + i);
                        blog.setContent("content-" + finalBatch + "-" + i);
                        blog.setAuthor("author-" + finalBatch);

                        jdbcTemplate.update(
                            "INSERT INTO blog (id, title, content, author) VALUES (?, ?, ?, ?)",
                            blog.getId(), blog.getTitle(), blog.getContent(), blog.getAuthor()
                        );

                        batchBlogs.add(blog);

                        // 模拟一些处理时间
                        Thread.sleep(10);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return batchBlogs;
            });
            futures.add(future);
        }

        // 等待所有批次完成并收集结果
        futures.forEach(future -> {
            try {
                createdBlogs.addAll(future.get());
            } catch (Exception e) {
                throw new RuntimeException("批量保存失败", e);
            }
        });

        return new BaseResponse<>(new Meta(true, "0", "success"), createdBlogs);
    }
}
