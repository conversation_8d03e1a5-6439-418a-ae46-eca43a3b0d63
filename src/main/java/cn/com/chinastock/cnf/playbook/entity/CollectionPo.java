package cn.com.chinastock.cnf.playbook.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.util.Date;


@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "COLLECTION")
@IdClass(CollectionKeyPo.class)
@JsonIgnoreProperties({"handler", "hibernateLazyInitializer"})
public class CollectionPo implements Serializable {

    /**
     * 客户号/服务号/手机号
     */
    @Column(name = "ACCT", length = 15)
    private String acct;

    /**
     * 系统代码
     */
    @Id
    @Column(name = "SYS_CODE", length = 5)
    private String sysCode;

    /**
     * 类型 C-客户号 T-手机 S-服务号
     */
    @Id
    @Column(name = "TYPE", length = 1)
    private String type;

    /**
     * 业务类型 0:收藏 1:点赞
     */
    @Id
    @Column(name = "BUSI_TYPE", length = 1)
    private String busiType;

    /**
     * 资讯ID
     */
    @Id
    @Column(name = "DATA_ID", length = 64)
    private String dataId;

    /**
     * 咨询标题
     */
    @Column(name = "DATA_TITLE", length = 100)
    private String dataTitle;

    /**
     * 资讯类型 1、新闻 2、公告 3、研报
     */
    @Id
    @Column(name = "DATA_TYPE", length = 2)
    private String dataType;

    /**
     * 是否有效 1：有效 0：无效
     */
    @Column(name = "IS_VALID", length = 1)
    private String isValid;

    /**
     * 创建时间
     */
    @Column(name = "CRT_TIME", length = 6)
    @CreationTimestamp
    private Date crtTime;

    /**
     * 最后修改时间
     */
    @Column(name = "UPD_TIME", length = 6)
    @UpdateTimestamp
    private Date updTime;

    /**
     * 文章原始时间
     */
    @Column(name = "ORI_TIME", length = 6)
    private Date oriTime;

    /**
     * 关联账户号的HASH编码
     */
    @Id
    @Column(name = "HASH_CODE", length = 64)
    private String hashCode;

    /**
     * 来源
     */
    @Column(name = "SRC", length = 100)
    private String src;

    /**
     * 客户号的sha256
     */
    @Column(name = "HASH_CODE2", length = 64)
    private String hashCode2;

    public String getAcct() {
        return acct;
    }

    public void setAcct(String acct) {
        this.acct = acct;
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String sysCode) {
        this.sysCode = sysCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getDataTitle() {
        return dataTitle;
    }

    public void setDataTitle(String dataTitle) {
        this.dataTitle = dataTitle;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public Date getCrtTime() {
        return crtTime;
    }

    public void setCrtTime(Date crtTime) {
        this.crtTime = crtTime;
    }

    public Date getUpdTime() {
        return updTime;
    }

    public void setUpdTime(Date updTime) {
        this.updTime = updTime;
    }

    public Date getOriTime() {
        return oriTime;
    }

    public void setOriTime(Date oriTime) {
        this.oriTime = oriTime;
    }

    public String getHashCode() {
        return hashCode;
    }

    public void setHashCode(String hashCode) {
        this.hashCode = hashCode;
    }

    public String getSrc() {
        return src;
    }

    public void setSrc(String src) {
        this.src = src;
    }

    public String getHashCode2() {
        return hashCode2;
    }

    public void setHashCode2(String hashCode2) {
        this.hashCode2 = hashCode2;
    }
}


