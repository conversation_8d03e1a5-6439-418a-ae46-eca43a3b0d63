package cn.com.chinastock.cnf.playbook.service;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.playbook.dto.KafkaBatchMessage;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Kafka消息生产者服务
 */
@Service
public class KafkaProducerService {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(KafkaProducerService.class);

    private final KafkaTemplate<String, String> kafkaTemplateForString;

    public KafkaProducerService(KafkaTemplate<String, String> kafkaTemplateForString, KafkaTemplate<Integer, String> kafkaTemplate) {
        this.kafkaTemplateForString = kafkaTemplateForString;
    }

    /**
     * 发送单个消息（使用String key）
     *
     * @return 发送结果的CompletableFuture
     */
    public CompletableFuture<SendResult<String, String>> sendMessage(String topic, String key, String message) {
        logger.info(LogCategory.APP_LOG, "Sending message to topic: {}, key: {}, message: {}",
                topic, key, message);

        CompletableFuture<SendResult<String, String>> kafkaFuture =
                kafkaTemplateForString.send(topic, key, message);

        return kafkaFuture.whenComplete((result, ex) -> {
            if (ex != null) {
                logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to topic: " + topic, ex);
            } else {
                logger.info(LogCategory.APP_LOG, "Message sent successfully to topic: {}, partition: {}, offset: {}",
                        result.getRecordMetadata().topic(),
                        result.getRecordMetadata().partition(),
                        result.getRecordMetadata().offset());
            }
        });
    }

    /**
     * 批量发送消息
     *
     * @param request 批量消息发送请求
     * @return 发送结果的CompletableFuture列表
     */
    public List<CompletableFuture<SendResult<String, String>>> sendBatchMessages(KafkaBatchMessage request) {
        logger.info(LogCategory.APP_LOG, "Sending batch messages to topic: {}, count: {}",
                request.getTopic(), request.getMessages().size());

        List<CompletableFuture<SendResult<String, String>>> futures = new ArrayList<>();

        for (KafkaBatchMessage.MessageItem item : request.getMessages()) {
            CompletableFuture<SendResult<String, String>> future = sendMessage(request.getTopic(), item.getKey(), item.getMessage());
            futures.add(future);
        }

        return futures;
    }

}
