package cn.com.chinastock.cnf.playbook.repository;

import cn.com.chinastock.cnf.playbook.entity.BlogPo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IBlogRepo extends JpaRepository<BlogPo, Long> {
    List<BlogPo> findByTitleContaining(String title);
    List<BlogPo> findByAuthor(String author);
    Page<BlogPo> findByAuthor(String author, Pageable pageable);

    @Query("SELECT b FROM BlogPo b WHERE b.author = :author AND b.title LIKE %:keyword%")
    List<BlogPo> findByAuthorAndTitleKeyword(@Param("author") String author, @Param("keyword") String keyword);

    @Query("SELECT COUNT(b) FROM BlogPo b GROUP BY b.author")
    List<Long> countBlogsGroupByAuthor();

    @Modifying
    @Query("UPDATE BlogPo b SET b.title = :newTitle WHERE b.author = :author")
    int updateTitlesByAuthor(@Param("author") String author, @Param("newTitle") String newTitle);

    @Modifying
    @Query("DELETE FROM BlogPo b WHERE b.author = :author")
    int deleteByAuthor(@Param("author") String author);
}
