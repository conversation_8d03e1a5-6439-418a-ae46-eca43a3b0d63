package cn.com.chinastock.cnf.playbook.client;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

@FeignClient(name = "bdp-service", url = "http://bdp-jdk21.tsph.chinastock.com.cn")
public interface BdpClient {

    @PostMapping(value = "/bdp_docker/userAsset/queryMyAssetDetail")
    BaseResponse<Object> queryMyAssetDetail(Map req);

}
