package cn.com.chinastock.cnf.playbook.controller;

import cn.com.chinastock.cnf.playbook.dto.RedisTestResult;
import cn.com.chinastock.cnf.playbook.service.RedisOperationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Redis操作测试控制器.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/test/redis")
public class RedisController {

    /** Redis操作测试服务. */
    private final RedisOperationService redisOperationService;

    public RedisController(RedisOperationService redisOperationService) {
        this.redisOperationService = redisOperationService;
    }

    /**
     * 测试String操作.
     *
     * @return 测试结果
     */
    @PostMapping("/string")
    public RedisTestResult testStringOperations() {
        return redisOperationService.testStringOperations();
    }

    /**
     * 测试Hash操作.
     *
     * @return 测试结果
     */
    @PostMapping("/hash")
    public RedisTestResult testHashOperations() {
        return redisOperationService.testHashOperations();
    }

    /**
     * 测试List操作.
     *
     * @return 测试结果
     */
    @PostMapping("/list")
    public RedisTestResult testListOperations() {
        return redisOperationService.testListOperations();
    }

    /**
     * 测试Set操作.
     *
     * @return 测试结果
     */
    @PostMapping("/set")
    public RedisTestResult testSetOperations() {
        return redisOperationService.testSetOperations();
    }

    /**
     * 测试ZSet操作.
     *
     * @return 测试结果
     */
    @PostMapping("/zset")
    public RedisTestResult testZSetOperations() {
        return redisOperationService.testZSetOperations();
    }

    /**
     * 测试过期操作.
     *
     * @return 测试结果
     */
    @PostMapping("/expiration")
    public RedisTestResult testExpirationOperations() {
        return redisOperationService.testExpirationOperations();
    }

    /**
     * 批量写入并批量读取String操作测试.
     * @return 测试结果
     */
    @PostMapping("/multiSetAndGet")
    public RedisTestResult testMultiSetAndGetOperations() {
        return redisOperationService.testMultiSetAndGetOperations();
    }

    /**
     * 运行所有Redis操作测试.
     *
     * @return 所有测试结果列表
     */
    @PostMapping("/operations/all")
    public List<RedisTestResult> runAllRedisOperationTests() {
        return redisOperationService.runAllRedisOperationTests();
    }

    /**
     * 测试默认序列化器（FastJson2）.
     *
     * @return 测试结果
     */
    @PostMapping("/serialization")
    public RedisTestResult testDefaultSerialization() {
        return redisOperationService.testObjectSerialization();
    }

}
