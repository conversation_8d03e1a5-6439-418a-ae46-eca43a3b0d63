package cn.com.chinastock.cnf.playbook.controller;

import cn.com.chinastock.cnf.playbook.dto.LogDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
class LogControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void shouldReturnCorrectResponseWhenCallGzipAPI() throws Exception {
        int length = 100;
        String expectedData = "a".repeat(length);

        mockMvc.perform(get("/api/test/log/gzip")
                .param("length", String.valueOf(length)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(expectedData));
    }

    @Test
    void shouldReturnCorrectResponseWhenCallBatchLogAPI() throws Exception {
        int length = 10;
        int count = 5;
        String expectedData = "a".repeat(length);

        mockMvc.perform(get("/api/test/log/batch")
                .param("length", String.valueOf(length))
                .param("count", String.valueOf(count)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(expectedData));
    }

    @Test
    void shouldReturnCorrectResponseWhenCallExceptionLogAPI() throws Exception {
        mockMvc.perform(get("/api/test/log/exception"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value("异常日志测试"));
    }

    @Test
    void shouldReturnCorrectResponseWhenCallMultipleLineLogAPI() throws Exception {
        String expectedData = """
                第一行日志
                第二行日志
                第三行日志
                """;

        mockMvc.perform(get("/api/test/log/multi-line"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(expectedData));
    }

    @Test
    void shouldReturnCorrectResponseWhenCallRequestBodyAPI() throws Exception {
        LogDTO logDTO = new LogDTO("测试数据");

        mockMvc.perform(post("/api/test/log/request-body")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(logDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value("测试数据"));
    }

    @Test
    void shouldReturnCorrectResponseWhenCallQueryStringAPIWithValidParameters() throws Exception {
        LogDTO logDTO = new LogDTO("测试数据");

        mockMvc.perform(post("/api/test/log/query-string")
                .param("level", "INFO")
                .param("category", "BUSINESS_LOG")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(logDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value("测试数据"));
    }

    @Test
    void shouldReturnCorrectResponseWhenCallQueryStringAPIWithDefaultCategory() throws Exception {
        LogDTO logDTO = new LogDTO("测试数据");

        mockMvc.perform(post("/api/test/log/query-string")
                .param("level", "INFO")
                .param("category", "default")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(logDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value("测试数据"));
    }

    @Test
    void shouldReturnServerErrorWhenCallQueryStringAPIWithInvalidLevel() throws Exception {
        LogDTO logDTO = new LogDTO("测试数据");

        mockMvc.perform(post("/api/test/log/query-string")
                .param("level", "INVALID_LEVEL")
                .param("category", "BUSINESS_LOG")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(logDTO)))
                .andExpect(status().is5xxServerError());
    }

    @Test
    void shouldReturnServerErrorWhenCallQueryStringAPIWithInvalidCategory() throws Exception {
        LogDTO logDTO = new LogDTO("测试数据");

        mockMvc.perform(post("/api/test/log/query-string")
                .param("level", "INFO")
                .param("category", "INVALID_CATEGORY")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(logDTO)))
                .andExpect(status().is5xxServerError());
    }
} 