<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGLM SSE Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .output {
            background-color: #f5f5f5;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: monospace;
            overflow-y: auto;
            max-height: 400px;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>ChatGLM SSE Test Client</h1>
    
    <div class="container">
        <h2>Test Configuration</h2>
        <label for="message">Message:</label>
        <input type="text" id="message" value="你好" style="width: 200px; padding: 5px; margin: 10px;">
        <br>
        <button class="button" onclick="testSSE()">Start SSE Test</button>
        <button class="button" onclick="clearOutput()">Clear Output</button>
    </div>

    <div class="container">
        <h2>Connection Status</h2>
        <div id="status" class="status info">Ready to test</div>
    </div>

    <div class="container">
        <h2>SSE Output</h2>
        <div id="output" class="output">Waiting for SSE events...</div>
    </div>

    <script>
        let eventSource = null;
        let outputDiv = document.getElementById('output');
        let statusDiv = document.getElementById('status');

        function updateStatus(message, type = 'info') {
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function appendOutput(message) {
            outputDiv.textContent += message + '\n';
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }

        function clearOutput() {
            outputDiv.textContent = 'Waiting for SSE events...\n';
        }

        function testSSE() {
            if (eventSource) {
                eventSource.close();
            }

            const message = document.getElementById('message').value;
            clearOutput();
            updateStatus('Connecting to SSE endpoint...', 'info');

            // Prepare the request data
            const requestData = {
                model: "glm-4-air",
                messages: [
                    {
                        role: "user",
                        content: message
                    }
                ],
                stream: true,
                temperature: 0.1
            };

            appendOutput(`[${new Date().toISOString()}] Starting SSE test with message: "${message}"`);
            appendOutput(`[${new Date().toISOString()}] Request data: ${JSON.stringify(requestData, null, 2)}`);

            // Use fetch to send POST request and handle SSE response
            fetch('http://localhost:8080/test/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                appendOutput(`[${new Date().toISOString()}] Response status: ${response.status}`);
                appendOutput(`[${new Date().toISOString()}] Response headers:`);
                for (let [key, value] of response.headers.entries()) {
                    appendOutput(`  ${key}: ${value}`);
                }

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                updateStatus('Connected! Receiving SSE data...', 'success');

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            appendOutput(`[${new Date().toISOString()}] Stream ended`);
                            updateStatus('Stream completed', 'success');
                            return;
                        }

                        const chunk = decoder.decode(value, { stream: true });
                        appendOutput(`[${new Date().toISOString()}] Received chunk: ${chunk}`);

                        // Parse SSE events
                        const lines = chunk.split('\n');
                        for (let line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.substring(6);
                                if (data === '[DONE]') {
                                    appendOutput(`[${new Date().toISOString()}] Received [DONE] signal`);
                                } else {
                                    try {
                                        const jsonData = JSON.parse(data);
                                        appendOutput(`[${new Date().toISOString()}] Parsed JSON: ${JSON.stringify(jsonData, null, 2)}`);
                                    } catch (e) {
                                        appendOutput(`[${new Date().toISOString()}] Non-JSON data: ${data}`);
                                    }
                                }
                            }
                        }

                        return readStream();
                    });
                }

                return readStream();
            })
            .catch(error => {
                appendOutput(`[${new Date().toISOString()}] Error: ${error.message}`);
                updateStatus(`Error: ${error.message}`, 'error');
            });
        }
    </script>
</body>
</html>
